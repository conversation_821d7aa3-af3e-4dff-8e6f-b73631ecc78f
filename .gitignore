# Commonly ignored file patterns.
# Customize this list according to your project's needs.
# Learn more about .gitignore: https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Logs
logs/
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Compiled language files
# Java class files
*.class
# Python bytecode
*.py[cod]

# OS-generated files
# MacOS
.DS_Store
# Windows
Thumbs.db

# IDE and Editor files
# JetBrains
.idea/
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
# Other IDEs
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Test coverage
/coverage
/.nyc_output
TEST*.xml

# Compiled output
/dist

# Environment variables
.env
.env.*

# Package files
*.jar
*.war

# Maven output
target/

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Application files
*.app
*.exe

# ORM configuration
/ormconfig.json

# Other data and file storage
/data
/files

docs/