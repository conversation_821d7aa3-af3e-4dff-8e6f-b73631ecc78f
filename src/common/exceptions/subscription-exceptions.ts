import { ForbiddenException } from '@nestjs/common';
import { ErrorCode } from './error-codes';

export class SubscriptionLimitExceededException extends ForbiddenException {
  constructor(resource?: string) {
    const message = resource 
      ? `You have reached the maximum number of ${resource} allowed in your subscription plan`
      : 'Subscription limit exceeded';
    super({
      message,
      errorCode: ErrorCode.SUBSCRIPTION_LIMIT_EXCEEDED,
    });
  }
}

export class FeatureNotAvailableException extends ForbiddenException {
  constructor(feature?: string) {
    const message = feature 
      ? `${feature} is not available in your current subscription plan`
      : 'This feature is not available in your current subscription plan';
    super({
      message,
      errorCode: ErrorCode.FEATURE_NOT_AVAILABLE,
    });
  }
}

export class WorkersLimitExceededException extends ForbiddenException {
  constructor() {
    super({
      message: 'You have reached the maximum number of workers allowed in your subscription plan',
      errorCode: ErrorCode.WORKERS_LIMIT_EXCEEDED,
    });
  }
}

export class ProjectsLimitExceededException extends ForbiddenException {
  constructor() {
    super({
      message: 'You have reached the maximum number of projects allowed in your subscription plan',
      errorCode: ErrorCode.PROJECTS_LIMIT_EXCEEDED,
    });
  }
}

export class ManagersLimitExceededException extends ForbiddenException {
  constructor() {
    super({
      message: 'You have reached the maximum number of managers allowed in your subscription plan',
      errorCode: ErrorCode.MANAGERS_LIMIT_EXCEEDED,
    });
  }
}

export class WebVersionNotAvailableException extends ForbiddenException {
  constructor() {
    super({
      message: 'Web version is not available in your current subscription plan',
      errorCode: ErrorCode.WEB_VERSION_NOT_AVAILABLE,
    });
  }
}

export class PhotoConfirmationNotAvailableException extends ForbiddenException {
  constructor() {
    super({
      message: 'Photo confirmation feature is not available in your current subscription plan',
      errorCode: ErrorCode.PHOTO_CONFIRMATION_NOT_AVAILABLE,
    });
  }
}

export class FinancialStatisticsNotAvailableException extends ForbiddenException {
  constructor() {
    super({
      message: 'Financial statistics are not available in your current subscription plan',
      errorCode: ErrorCode.FINANCIAL_STATISTICS_NOT_AVAILABLE,
    });
  }
}

export class ChartsNotAvailableException extends ForbiddenException {
  constructor() {
    super({
      message: 'Charts feature is not available in your current subscription plan',
      errorCode: ErrorCode.CHARTS_NOT_AVAILABLE,
    });
  }
}

export class AdvancedAnalyticsNotAvailableException extends ForbiddenException {
  constructor() {
    super({
      message: 'Advanced analytics are not available in your current subscription plan',
      errorCode: ErrorCode.ADVANCED_ANALYTICS_NOT_AVAILABLE,
    });
  }
}

export class ReportGenerationNotAvailableException extends ForbiddenException {
  constructor() {
    super({
      message: 'Report generation is not available in your current subscription plan',
      errorCode: ErrorCode.REPORT_GENERATION_NOT_AVAILABLE,
    });
  }
}
