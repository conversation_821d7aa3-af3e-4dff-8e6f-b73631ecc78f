import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { ErrorCode, ErrorMessages } from './error-codes';

export interface StandardErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  errorCode: ErrorCode;
  timestamp: string;
  path: string;
  validationErrors?: Record<string, string[]>;
}

export class StandardHttpException extends HttpException {
  constructor(
    public readonly errorCode: ErrorCode,
    status: HttpStatus,
    message?: string,
  ) {
    super(message || ErrorMessages[errorCode], status);
  }

  getErrorCode(): ErrorCode {
    return this.errorCode;
  }
}

export class InvalidCredentialsException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_CREDENTIALS]);
    this.name = 'InvalidCredentialsException';
  }
}

export class TokenExpiredException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.TOKEN_EXPIRED]);
    this.name = 'TokenExpiredException';
  }
}

export class InvalidTokenException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_TOKEN]);
    this.name = 'InvalidTokenException';
  }
}

export class EmailNotVerifiedException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.EMAIL_NOT_VERIFIED]);
    this.name = 'EmailNotVerifiedException';
  }
}

export class UserNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.USER_NOT_FOUND]);
    this.name = 'UserNotFoundException';
  }
}

export class EmailAlreadyExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.EMAIL_ALREADY_EXISTS]);
    this.name = 'EmailAlreadyExistsException';
  }
}

export class EmailUnchangedException extends StandardHttpException {
  constructor() {
    super(ErrorCode.EMAIL_UNCHANGED, HttpStatus.BAD_REQUEST);
    this.name = 'EmailUnchangedException';
  }
}

export class InvalidPasswordException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_PASSWORD]);
    this.name = 'InvalidPasswordException';
  }
}

export class PasswordRequiredException extends StandardHttpException {
  constructor() {
    super(ErrorCode.PASSWORD_REQUIRED, HttpStatus.BAD_REQUEST);
    this.name = 'PasswordRequiredException';
  }
}

export class PartnerNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PARTNER_NOT_FOUND]);
    this.name = 'PartnerNotFoundException';
  }
}

export class TaxNumberAlreadyExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.TAX_NUMBER_ALREADY_EXISTS]);
    this.name = 'TaxNumberAlreadyExistsException';
  }
}

export class WorkerNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_NOT_FOUND]);
    this.name = 'WorkerNotFoundException';
  }
}

export class WorkerNotEmployedException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_NOT_EMPLOYED, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerNotEmployedException';
  }
}

export class WorkerNotApprovedException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_NOT_APPROVED, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerNotApprovedException';
  }
}

export class WorkerNotAssignedToProjectException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_NOT_ASSIGNED_TO_PROJECT, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerNotAssignedToProjectException';
  }
}

export class WorkerAlreadyPausedException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_ALREADY_PAUSED, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerAlreadyPausedException';
  }
}

export class WorkerAlreadyActiveException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_ALREADY_ACTIVE, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerAlreadyActiveException';
  }
}

export class WorkerAlreadyFinishedException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_ALREADY_FINISHED, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerAlreadyFinishedException';
  }
}

export class ManagerNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.MANAGER_NOT_FOUND]);
    this.name = 'ManagerNotFoundException';
  }
}

export class InsufficientManagerPermissionsException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.INSUFFICIENT_MANAGER_PERMISSIONS]);
    this.name = 'InsufficientManagerPermissionsException';
  }
}

export class ProjectNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PROJECT_NOT_FOUND]);
    this.name = 'ProjectNotFoundException';
  }
}

export class ScheduleNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.SCHEDULE_NOT_FOUND]);
    this.name = 'ScheduleNotFoundException';
  }
}

export class DailyReportNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.DAILY_REPORT_NOT_FOUND]);
    this.name = 'DailyReportNotFoundException';
  }
}

export class ActiveReportExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.ACTIVE_REPORT_EXISTS]);
    this.name = 'ActiveReportExistsException';
  }
}

export class NoActiveReportException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.NO_ACTIVE_REPORT]);
    this.name = 'NoActiveReportException';
  }
}

export class ReportOnPauseException extends StandardHttpException {
  constructor() {
    super(ErrorCode.REPORT_ON_PAUSE, HttpStatus.BAD_REQUEST);
    this.name = 'ReportOnPauseException';
  }
}

export class SubmittedTimeMismatchException extends StandardHttpException {
  constructor() {
    super(ErrorCode.SUBMITTED_TIME_MISMATCH, HttpStatus.BAD_REQUEST);
    this.name = 'SubmittedTimeMismatchException';
  }
}

export class PresenceValidationNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PRESENCE_VALIDATION_NOT_FOUND]);
    this.name = 'PresenceValidationNotFoundException';
  }
}

export class ValidationRequirementsMissingException extends StandardHttpException {
  constructor() {
    super(ErrorCode.VALIDATION_REQUIREMENTS_MISSING, HttpStatus.BAD_REQUEST);
    this.name = 'ValidationRequirementsMissingException';
  }
}

export class WorkerIdsEmptyException extends StandardHttpException {
  constructor() {
    super(ErrorCode.WORKER_IDS_EMPTY, HttpStatus.BAD_REQUEST);
    this.name = 'WorkerIdsEmptyException';
  }
}

export class RegistrationCodeNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.REGISTRATION_CODE_NOT_FOUND]);
    this.name = 'RegistrationCodeNotFoundException';
  }
}

export class RegistrationRequestNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.REGISTRATION_REQUEST_NOT_FOUND]);
    this.name = 'RegistrationRequestNotFoundException';
  }
}

export class FileNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.FILE_NOT_FOUND]);
    this.name = 'FileNotFoundException';
  }
}

export class InvalidFileTypeException extends BadRequestException {
  constructor(allowedTypes?: string[]) {
    const message = allowedTypes
      ? `${ErrorMessages[ErrorCode.INVALID_FILE_TYPE]}. Allowed types: ${allowedTypes.join(', ')}`
      : ErrorMessages[ErrorCode.INVALID_FILE_TYPE];
    super(message);
    this.name = 'InvalidFileTypeException';
  }
}

export class SessionNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.SESSION_NOT_FOUND]);
    this.name = 'SessionNotFoundException';
  }
}

export class InvalidVerificationTypeException extends StandardHttpException {
  constructor() {
    super(ErrorCode.INVALID_VERIFICATION_TYPE, HttpStatus.BAD_REQUEST);
    this.name = 'InvalidVerificationTypeException';
  }
}

export class InvalidRegistrationCodeException extends StandardHttpException {
  constructor() {
    super(ErrorCode.INVALID_REGISTRATION_CODE, HttpStatus.BAD_REQUEST);
    this.name = 'InvalidRegistrationCodeException';
  }
}

export class RegistrationCodeRequiredException extends StandardHttpException {
  constructor() {
    super(ErrorCode.REGISTRATION_CODE_REQUIRED, HttpStatus.BAD_REQUEST);
    this.name = 'RegistrationCodeRequiredException';
  }
}

export class PasswordResetTokenNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PASSWORD_RESET_TOKEN_NOT_FOUND]);
    this.name = 'PasswordResetTokenNotFoundException';
  }
}

export class PasswordResetTokenExpiredException extends StandardHttpException {
  constructor() {
    super(ErrorCode.PASSWORD_RESET_TOKEN_EXPIRED, HttpStatus.BAD_REQUEST);
    this.name = 'PasswordResetTokenExpiredException';
  }
}

export class EmptyArrayException extends BadRequestException {
  constructor(message: string = 'Array is empty') {
    super(message);
    this.name = 'EmptyArrayException';
  }
}

export class SupervisorNotFoundException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.SUPERVISOR_NOT_FOUND]);
    this.name = 'SupervisorNotFoundException';
  }
}

export class SupervisorMismatchException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.SUPERVISOR_MISMATCH]);
    this.name = 'SupervisorMismatchException';
  }
}

export class ProjectNotManagedByManagerException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.PROJECT_NOT_MANAGED_BY_MANAGER]);
    this.name = 'ProjectNotManagedByManagerException';
  }
}

export class ForbiddenToFinishReportException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.FORBIDDEN_TO_FINISH_REPORT]);
    this.name = 'ForbiddenToFinishReportException';
  }
}

export class ForbiddenToCreateManualReportException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.FORBIDDEN_TO_CREATE_MANUAL_REPORT]);
    this.name = 'ForbiddenToCreateManualReportException';
  }
}

export class NotAuthorizedException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.NOT_AUTHORIZED]);
    this.name = 'NotAuthorizedException';
  }
}

export class ForbiddenRoleException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.FORBIDDEN_ROLE]);
    this.name = 'ForbiddenRoleException';
  }
}

export class PauseHistoryNotFoundException extends NotFoundException {
  constructor() {
    super('Pause history entry not found');
    this.name = 'PauseHistoryNotFoundException';
  }
}

export class WorkerAlreadyTerminatedException extends BadRequestException {
  constructor() {
    super('Worker is already terminated');
    this.name = 'WorkerAlreadyTerminatedException';
  }
}

export class WorkerOnNoticeException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ON_NOTICE]);
    this.name = 'WorkerOnNoticeException';
  }
}

export class WorkerAlreadyEmployedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ALREADY_EMPLOYED]);
    this.name = 'WorkerAlreadyEmployedException';
  }
}

export class InvalidWorkerIdException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_WORKER_ID]);
    this.name = 'InvalidWorkerIdException';
  }
}

export class NotificationNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.NOTIFICATION_NOT_FOUND]);
    this.name = 'NotificationNotFoundException';
  }
}

export class NotificationAlreadyReadException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.NOTIFICATION_ALREADY_READ]);
    this.name = 'NotificationAlreadyReadException';
  }
}

export class EmploymentAlreadyActiveException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.EMPLOYMENT_ALREADY_ACTIVE]);
    this.name = 'EmploymentAlreadyActiveException';
  }
}

export class ActivityHistoryNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.ACTIVITY_HISTORY_NOT_FOUND]);
    this.name = 'ActivityHistoryNotFoundException';
  }
}

export class FilePermissionNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.FILE_PERMISSION_NOT_FOUND]);
    this.name = 'FilePermissionNotFoundException';
  }
}

export class FilePermissionAlreadyExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.FILE_PERMISSION_ALREADY_EXISTS]);
    this.name = 'FilePermissionAlreadyExistsException';
  }
}

export class InsufficientFilePermissionsException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.INSUFFICIENT_FILE_PERMISSIONS]);
    this.name = 'InsufficientFilePermissionsException';
  }
}

export class ProjectManagerAssignmentNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PROJECT_MANAGER_ASSIGNMENT_NOT_FOUND]);
    this.name = 'ProjectManagerAssignmentNotFoundException';
  }
}

export class ProjectManagerAlreadyAssignedException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.PROJECT_MANAGER_ALREADY_ASSIGNED]);
    this.name = 'ProjectManagerAlreadyAssignedException';
  }
}

export class CannotRemoveLastManagerException extends StandardHttpException {
  constructor() {
    super(ErrorCode.CANNOT_REMOVE_LAST_MANAGER, HttpStatus.BAD_REQUEST);
    this.name = 'CannotRemoveLastManagerException';
  }
}

export class FileSizeExceededException extends BadRequestException {
  constructor(maxSize?: string) {
    const message = maxSize
      ? `${ErrorMessages[ErrorCode.FILE_SIZE_EXCEEDED]}. Maximum allowed: ${maxSize}`
      : ErrorMessages[ErrorCode.FILE_SIZE_EXCEEDED];
    super(message);
    this.name = 'FileSizeExceededException';
  }
}

export class UnsupportedFileFormatException extends BadRequestException {
  constructor(allowedFormats?: string[]) {
    const message = allowedFormats
      ? `${ErrorMessages[ErrorCode.UNSUPPORTED_FILE_FORMAT]}. Allowed formats: ${allowedFormats.join(', ')}`
      : ErrorMessages[ErrorCode.UNSUPPORTED_FILE_FORMAT];
    super(message);
    this.name = 'UnsupportedFileFormatException';
  }
}

export class FileCorruptedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.FILE_CORRUPTED]);
    this.name = 'FileCorruptedException';
  }
}

export class StorageQuotaExceededException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.STORAGE_QUOTA_EXCEEDED]);
    this.name = 'StorageQuotaExceededException';
  }
}

export class OperationNotAllowedException extends BadRequestException {
  constructor(operation?: string) {
    const message = operation
      ? `${ErrorMessages[ErrorCode.OPERATION_NOT_ALLOWED]}: ${operation}`
      : ErrorMessages[ErrorCode.OPERATION_NOT_ALLOWED];
    super(message);
    this.name = 'OperationNotAllowedException';
  }
}

export class ForbiddenResourceException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.FORBIDDEN_RESOURCE]);
    this.name = 'ForbiddenResourceException';
  }
}

export class ResourceInUseException extends ConflictException {
  constructor(resource?: string) {
    const message = resource
      ? `${ErrorMessages[ErrorCode.RESOURCE_IN_USE]}: ${resource}`
      : ErrorMessages[ErrorCode.RESOURCE_IN_USE];
    super(message);
    this.name = 'ResourceInUseException';
  }
}

export class DependencyNotMetException extends BadRequestException {
  constructor(dependency?: string) {
    const message = dependency
      ? `${ErrorMessages[ErrorCode.DEPENDENCY_NOT_MET]}: ${dependency}`
      : ErrorMessages[ErrorCode.DEPENDENCY_NOT_MET];
    super(message);
    this.name = 'DependencyNotMetException';
  }
}

export class QuotaExceededException extends BadRequestException {
  constructor(quotaType?: string) {
    const message = quotaType
      ? `${ErrorMessages[ErrorCode.QUOTA_EXCEEDED]}: ${quotaType}`
      : ErrorMessages[ErrorCode.QUOTA_EXCEEDED];
    super(message);
    this.name = 'QuotaExceededException';
  }
}

export class DeviceNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.DEVICE_NOT_FOUND]);
    this.name = 'DeviceNotFoundException';
  }
}

export class DeviceAlreadyRegisteredException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.DEVICE_ALREADY_REGISTERED]);
    this.name = 'DeviceAlreadyRegisteredException';
  }
}

export class InvalidDeviceTokenException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_DEVICE_TOKEN]);
    this.name = 'InvalidDeviceTokenException';
  }
}

export class DeviceNotOwnedException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.DEVICE_NOT_OWNED]);
    this.name = 'DeviceNotOwnedException';
  }
}

export class ReportOnPauseValidationException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.REPORT_ON_PAUSE_VALIDATION]);
    this.name = 'ReportOnPauseValidationException';
  }
}

export class ValidationRequirementsMissingDetailedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.VALIDATION_REQUIREMENTS_MISSING_DETAILED]);
    this.name = 'ValidationRequirementsMissingDetailedException';
  }
}

export class WorkerIdsEmptyDetailedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_IDS_EMPTY_DETAILED]);
    this.name = 'WorkerIdsEmptyDetailedException';
  }
}

export class GeoCoordinatesRequiredException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.GEO_COORDINATES_REQUIRED]);
    this.name = 'GeoCoordinatesRequiredException';
  }
}

export class PhotoRequiredException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.PHOTO_REQUIRED]);
    this.name = 'PhotoRequiredException';
  }
}

export class WorkerIdSpecificationErrorException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ID_SPECIFICATION_ERROR]);
    this.name = 'WorkerIdSpecificationErrorException';
  }
}

export class WorkerIdRequiredException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ID_REQUIRED]);
    this.name = 'WorkerIdRequiredException';
  }
}

export class ManagerPermissionRestrictionException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.MANAGER_PERMISSION_RESTRICTION]);
    this.name = 'ManagerPermissionRestrictionException';
  }
}

export class InvalidUserContextException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_USER_CONTEXT]);
    this.name = 'InvalidUserContextException';
  }
}

export class InvalidUserEntityException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_USER_ENTITY]);
    this.name = 'InvalidUserEntityException';
  }
}

export class RateLimitPresenceValidationException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.RATE_LIMIT_PRESENCE_VALIDATION]);
    this.name = 'RateLimitPresenceValidationException';
  }
}

export class ValidationRequestExpiredException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.VALIDATION_REQUEST_EXPIRED]);
    this.name = 'ValidationRequestExpiredException';
  }
}

export class PresenceValidationTooLateException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.PRESENCE_VALIDATION_TOO_LATE]);
    this.name = 'PresenceValidationTooLateException';
  }
}

export class CodeNameAlreadyExistsException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.CODE_NAME_ALREADY_EXISTS]);
    this.name = 'CodeNameAlreadyExistsException';
  }
}

export class CredentialVerificationNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.CREDENTIAL_VERIFICATION_NOT_FOUND]);
    this.name = 'CredentialVerificationNotFoundException';
  }
}

export class CredentialVerificationExpiredException extends StandardHttpException {
  constructor() {
    super(ErrorCode.CREDENTIAL_VERIFICATION_EXPIRED, HttpStatus.BAD_REQUEST);
    this.name = 'CredentialVerificationExpiredException';
  }
}

export class CredentialVerificationAlreadyUsedException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.CREDENTIAL_VERIFICATION_ALREADY_USED]);
    this.name = 'CredentialVerificationAlreadyUsedException';
  }
}

export class InvalidVerificationCodeException extends StandardHttpException {
  constructor() {
    super(ErrorCode.INVALID_VERIFICATION_CODE, HttpStatus.BAD_REQUEST);
    this.name = 'InvalidVerificationCodeException';
  }
}

export class PresenceValidationExpiredException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.PRESENCE_VALIDATION_EXPIRED]);
    this.name = 'PresenceValidationExpiredException';
  }
}

export class PresenceValidationAlreadyCompletedException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.PRESENCE_VALIDATION_ALREADY_COMPLETED]);
    this.name = 'PresenceValidationAlreadyCompletedException';
  }
}

export class InvalidPresenceDataException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_PRESENCE_DATA]);
    this.name = 'InvalidPresenceDataException';
  }
}
