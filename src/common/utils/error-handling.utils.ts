import { HttpStatus, HttpException } from '@nestjs/common';

import {
  DailyReportNotFoundException,
  EmailAlreadyExistsException,
  FileNotFoundException,
  InvalidCredentialsException,
  ManagerNotFoundException,
  PartnerNotFoundException,
  ProjectNotFoundException,
  UserNotFoundException,
  WorkerNotFoundException,
  StandardHttpException,
} from '../exceptions/custom-exceptions';
import { ErrorCode, ErrorMessages } from '../exceptions/error-codes';

/**
 * Utility functions for consistent error handling across the application
 */
export class ErrorHandlingUtils {
  /**
   * Throws appropriate exception if entity is not found
   * @param entity - The entity to check
   * @param entityType - Type of entity for appropriate exception
   * @param customMessage - Optional custom error message
   */
  static throwIfNotFound<T>(
    entity: T | null | undefined,
    entityType:
      | 'user'
      | 'worker'
      | 'partner'
      | 'manager'
      | 'project'
      | 'dailyReport'
      | 'file',
    customMessage?: string,
  ): asserts entity is T {
    if (!entity) {
      switch (entityType) {
        case 'user':
          throw new UserNotFoundException();
        case 'worker':
          throw new WorkerNotFoundException();
        case 'partner':
          throw new PartnerNotFoundException();
        case 'manager':
          throw new ManagerNotFoundException();
        case 'project':
          throw new ProjectNotFoundException();
        case 'dailyReport':
          throw new DailyReportNotFoundException();
        case 'file':
          throw new FileNotFoundException();
        default:
          throw new StandardHttpException(
            ErrorCode.NOT_FOUND,
            HttpStatus.NOT_FOUND,
            customMessage,
          );
      }
    }
  }

  /**
   * Throws appropriate exception if entity already exists
   * @param entity - The entity to check
   * @param entityType - Type of entity for appropriate exception
   * @param customMessage - Optional custom error message
   */
  static throwIfExists<T>(
    entity: T | null | undefined,
    entityType: 'email' | 'taxNumber' | 'registrationCode',
    customMessage?: string,
  ): void {
    if (entity) {
      switch (entityType) {
        case 'email':
          throw new EmailAlreadyExistsException();
        default:
          throw new StandardHttpException(
            ErrorCode.CONFLICT,
            HttpStatus.CONFLICT,
            customMessage,
          );
      }
    }
  }

  /**
   * Validates user credentials and throws appropriate exception
   * @param isValid - Whether credentials are valid
   * @param customMessage - Optional custom error message
   */
  static validateCredentials(isValid: boolean, customMessage?: string): void {
    if (!isValid) {
      throw new InvalidCredentialsException();
    }
  }

  /**
   * Validates array is not empty
   * @param array - Array to validate
   * @param fieldName - Name of the field for error message
   */
  static validateArrayNotEmpty<T>(array: T[], fieldName: string): void {
    if (!array || array.length === 0) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        HttpStatus.BAD_REQUEST,
        `${fieldName} cannot be empty`,
      );
    }
  }

  /**
   * Validates required fields are present
   * @param fields - Object with field names and values
   */
  static validateRequiredFields(fields: Record<string, any>): void {
    const missingFields = Object.entries(fields)
      .filter(
        ([_, value]) => value === null || value === undefined || value === '',
      )
      .map(([key, _]) => key);

    if (missingFields.length > 0) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        HttpStatus.BAD_REQUEST,
        `Required fields missing: ${missingFields.join(', ')}`,
      );
    }
  }

  /**
   * Validates user permissions for an operation
   * @param hasPermission - Whether user has permission
   * @param operation - Description of the operation
   */
  static validatePermissions(hasPermission: boolean, operation?: string): void {
    if (!hasPermission) {
      const message = operation
        ? `Insufficient permissions for: ${operation}`
        : 'Insufficient permissions';
      throw new StandardHttpException(
        ErrorCode.FORBIDDEN,
        HttpStatus.FORBIDDEN,
        message,
      );
    }
  }

  /**
   * Validates business rules and throws appropriate exception
   * @param isValid - Whether business rule is satisfied
   * @param errorMessage - Error message to throw
   * @param statusCode - HTTP status code (default: 400)
   */
  static validateBusinessRule(
    isValid: boolean,
    errorMessage: string,
    statusCode: HttpStatus = HttpStatus.BAD_REQUEST,
  ): void {
    if (!isValid) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        statusCode,
        errorMessage,
      );
    }
  }

  /**
   * Wraps async operations with error handling
   * @param operation - Async operation to execute
   * @param errorMessage - Custom error message on failure
   */
  static async wrapAsync<T>(
    operation: () => Promise<T>,
    errorMessage?: string,
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new StandardHttpException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        errorMessage,
      );
    }
  }

  /**
   * Creates a standardized error response object
   * @param errorCode - Standardized error code
   * @param statusCode - HTTP status code
   * @param path - Request path
   * @param validationErrors - Optional validation errors
   */
  static createErrorResponse(
    errorCode: ErrorCode,
    statusCode: HttpStatus,
    path: string,
    validationErrors?: Record<string, string[]>,
  ) {
    return {
      statusCode,
      message: ErrorMessages[errorCode],
      error: this.getErrorName(statusCode),
      errorCode,
      timestamp: new Date().toISOString(),
      path,
      ...(validationErrors && { validationErrors }),
    };
  }

  /**
   * Gets error name from HTTP status code
   * @param statusCode - HTTP status code
   */
  private static getErrorName(statusCode: HttpStatus): string {
    switch (statusCode) {
      case HttpStatus.BAD_REQUEST:
        return 'Bad Request';
      case HttpStatus.UNAUTHORIZED:
        return 'Unauthorized';
      case HttpStatus.FORBIDDEN:
        return 'Forbidden';
      case HttpStatus.NOT_FOUND:
        return 'Not Found';
      case HttpStatus.CONFLICT:
        return 'Conflict';
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return 'Internal Server Error';
      default:
        return 'Error';
    }
  }

  /**
   * Validates file upload constraints
   * @param file - Uploaded file
   * @param maxSize - Maximum file size in bytes
   * @param allowedTypes - Array of allowed file extensions
   */
  static validateFileUpload(
    // @ts-expect-error - Express.Multer.File is not defined in the global scope
    file: Express.Multer.File,
    maxSize: number,
    allowedTypes: string[],
  ): void {
    if (!file) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        HttpStatus.BAD_REQUEST,
        'File is required',
      );
    }

    if (file.size > maxSize) {
      throw new StandardHttpException(
        ErrorCode.FILE_TOO_LARGE,
        HttpStatus.BAD_REQUEST,
        `File size exceeds maximum limit of ${Math.round(maxSize / (1024 * 1024))}MB`,
      );
    }

    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    if (!fileExtension || !allowedTypes.includes(`.${fileExtension}`)) {
      throw new StandardHttpException(
        ErrorCode.INVALID_FILE_TYPE,
        HttpStatus.BAD_REQUEST,
        `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`,
      );
    }
  }

  /**
   * Validates pagination parameters
   * @param page - Page number
   * @param limit - Items per page
   * @param maxLimit - Maximum allowed limit
   */
  static validatePagination(
    page: number,
    limit: number,
    maxLimit: number = 100,
  ): void {
    if (page < 1) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        HttpStatus.BAD_REQUEST,
        'Page must be greater than 0',
      );
    }

    if (limit < 1) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        HttpStatus.BAD_REQUEST,
        'Limit must be greater than 0',
      );
    }

    if (limit > maxLimit) {
      throw new StandardHttpException(
        ErrorCode.BAD_REQUEST,
        HttpStatus.BAD_REQUEST,
        `Limit cannot exceed ${maxLimit}`,
      );
    }
  }

  /**
   * Validates date range
   * @param startDate - Start date
   * @param endDate - End date
   * @param maxRangeDays - Maximum allowed range in days
   */
  static validateDateRange(
    startDate: Date,
    endDate: Date,
    maxRangeDays: number = 365,
  ): void {
    if (startDate > endDate) {
      throw new HttpException(
        'Start date cannot be after end date',
        HttpStatus.BAD_REQUEST,
      );
    }

    const daysDiff = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
    );

    if (daysDiff > maxRangeDays) {
      throw new HttpException(
        `Date range cannot exceed ${maxRangeDays} days`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
