/**
 * Validation error codes for Zod schema validation errors
 * Each validation error has a specific code that the frontend can reference
 */
export enum ValidationErrorCode {
  // String validation errors
  STRING_TOO_SHORT = 'STRING_TOO_SHORT',
  STRING_TOO_LONG = 'STRING_TOO_LONG',
  STRING_INVALID_FORMAT = 'STRING_INVALID_FORMAT',
  STRING_REQUIRED = 'STRING_REQUIRED',
  
  // Email validation errors
  EMAIL_INVALID_FORMAT = 'EMAIL_INVALID_FORMAT',
  EMAIL_REQUIRED = 'EMAIL_REQUIRED',
  
  // Password validation errors
  PASSWORD_TOO_SHORT = 'PASSWORD_TOO_SHORT',
  PASSWORD_TOO_LONG = 'PASSWORD_TOO_LONG',
  PASSWORD_REQUIRED = 'PASSWORD_REQUIRED',
  PASSWORD_INVALID_FORMAT = 'PASSWORD_INVALID_FORMAT',
  
  // Phone number validation errors
  PHONE_INVALID_FORMAT = 'PHONE_INVALID_FORMAT',
  PHONE_INVALID_COUNTRY_CODE = 'PHONE_INVALID_COUNTRY_CODE',
  PHONE_TOO_SHORT = 'PHONE_TOO_SHORT',
  PHONE_TOO_LONG = 'PHONE_TOO_LONG',
  PHONE_REQUIRED = 'PHONE_REQUIRED',
  
  // Number validation errors
  NUMBER_TOO_SMALL = 'NUMBER_TOO_SMALL',
  NUMBER_TOO_BIG = 'NUMBER_TOO_BIG',
  NUMBER_INVALID_FORMAT = 'NUMBER_INVALID_FORMAT',
  NUMBER_REQUIRED = 'NUMBER_REQUIRED',
  
  // Date validation errors
  DATE_INVALID_FORMAT = 'DATE_INVALID_FORMAT',
  DATE_TOO_EARLY = 'DATE_TOO_EARLY',
  DATE_TOO_LATE = 'DATE_TOO_LATE',
  DATE_REQUIRED = 'DATE_REQUIRED',
  
  // Boolean validation errors
  BOOLEAN_INVALID_FORMAT = 'BOOLEAN_INVALID_FORMAT',
  BOOLEAN_REQUIRED = 'BOOLEAN_REQUIRED',
  
  // Array validation errors
  ARRAY_TOO_SHORT = 'ARRAY_TOO_SHORT',
  ARRAY_TOO_LONG = 'ARRAY_TOO_LONG',
  ARRAY_REQUIRED = 'ARRAY_REQUIRED',
  ARRAY_INVALID_ITEM = 'ARRAY_INVALID_ITEM',
  
  // Object validation errors
  OBJECT_REQUIRED = 'OBJECT_REQUIRED',
  OBJECT_INVALID_FORMAT = 'OBJECT_INVALID_FORMAT',
  
  // Enum validation errors
  ENUM_INVALID_VALUE = 'ENUM_INVALID_VALUE',
  ENUM_REQUIRED = 'ENUM_REQUIRED',
  
  // Union validation errors
  UNION_INVALID_VALUE = 'UNION_INVALID_VALUE',
  UNION_REQUIRED = 'UNION_REQUIRED',
  
  // Custom validation errors
  CUSTOM_VALIDATION_FAILED = 'CUSTOM_VALIDATION_FAILED',
  REFINE_VALIDATION_FAILED = 'REFINE_VALIDATION_FAILED',
  
  // File validation errors
  FILE_REQUIRED = 'FILE_REQUIRED',
  FILE_INVALID_TYPE = 'FILE_INVALID_TYPE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  
  // Citizenship/Country validation errors
  CITIZENSHIP_INVALID = 'CITIZENSHIP_INVALID',
  CITIZENSHIP_REQUIRED = 'CITIZENSHIP_REQUIRED',
  
  // Generic validation errors
  FIELD_REQUIRED = 'FIELD_REQUIRED',
  FIELD_INVALID = 'FIELD_INVALID',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
}

/**
 * Human-readable error messages for validation error codes
 */
export const ValidationErrorMessages: Record<ValidationErrorCode, string> = {
  // String validation errors
  [ValidationErrorCode.STRING_TOO_SHORT]: 'String is too short',
  [ValidationErrorCode.STRING_TOO_LONG]: 'String is too long',
  [ValidationErrorCode.STRING_INVALID_FORMAT]: 'String format is invalid',
  [ValidationErrorCode.STRING_REQUIRED]: 'String is required',
  
  // Email validation errors
  [ValidationErrorCode.EMAIL_INVALID_FORMAT]: 'Email format is invalid',
  [ValidationErrorCode.EMAIL_REQUIRED]: 'Email is required',
  
  // Password validation errors
  [ValidationErrorCode.PASSWORD_TOO_SHORT]: 'Password is too short (minimum 8 characters)',
  [ValidationErrorCode.PASSWORD_TOO_LONG]: 'Password is too long',
  [ValidationErrorCode.PASSWORD_REQUIRED]: 'Password is required',
  [ValidationErrorCode.PASSWORD_INVALID_FORMAT]: 'Password format is invalid',
  
  // Phone number validation errors
  [ValidationErrorCode.PHONE_INVALID_FORMAT]: 'Phone number format is invalid',
  [ValidationErrorCode.PHONE_INVALID_COUNTRY_CODE]: 'Phone number must start with a valid country code',
  [ValidationErrorCode.PHONE_TOO_SHORT]: 'Phone number is too short',
  [ValidationErrorCode.PHONE_TOO_LONG]: 'Phone number is too long',
  [ValidationErrorCode.PHONE_REQUIRED]: 'Phone number is required',
  
  // Number validation errors
  [ValidationErrorCode.NUMBER_TOO_SMALL]: 'Number is too small',
  [ValidationErrorCode.NUMBER_TOO_BIG]: 'Number is too big',
  [ValidationErrorCode.NUMBER_INVALID_FORMAT]: 'Number format is invalid',
  [ValidationErrorCode.NUMBER_REQUIRED]: 'Number is required',
  
  // Date validation errors
  [ValidationErrorCode.DATE_INVALID_FORMAT]: 'Date format is invalid',
  [ValidationErrorCode.DATE_TOO_EARLY]: 'Date is too early',
  [ValidationErrorCode.DATE_TOO_LATE]: 'Date is too late',
  [ValidationErrorCode.DATE_REQUIRED]: 'Date is required',
  
  // Boolean validation errors
  [ValidationErrorCode.BOOLEAN_INVALID_FORMAT]: 'Boolean format is invalid',
  [ValidationErrorCode.BOOLEAN_REQUIRED]: 'Boolean is required',
  
  // Array validation errors
  [ValidationErrorCode.ARRAY_TOO_SHORT]: 'Array is too short',
  [ValidationErrorCode.ARRAY_TOO_LONG]: 'Array is too long',
  [ValidationErrorCode.ARRAY_REQUIRED]: 'Array is required',
  [ValidationErrorCode.ARRAY_INVALID_ITEM]: 'Array contains invalid item',
  
  // Object validation errors
  [ValidationErrorCode.OBJECT_REQUIRED]: 'Object is required',
  [ValidationErrorCode.OBJECT_INVALID_FORMAT]: 'Object format is invalid',
  
  // Enum validation errors
  [ValidationErrorCode.ENUM_INVALID_VALUE]: 'Invalid enum value',
  [ValidationErrorCode.ENUM_REQUIRED]: 'Enum value is required',
  
  // Union validation errors
  [ValidationErrorCode.UNION_INVALID_VALUE]: 'Invalid union value',
  [ValidationErrorCode.UNION_REQUIRED]: 'Union value is required',
  
  // Custom validation errors
  [ValidationErrorCode.CUSTOM_VALIDATION_FAILED]: 'Custom validation failed',
  [ValidationErrorCode.REFINE_VALIDATION_FAILED]: 'Validation rule failed',
  
  // File validation errors
  [ValidationErrorCode.FILE_REQUIRED]: 'File is required',
  [ValidationErrorCode.FILE_INVALID_TYPE]: 'File type is invalid',
  [ValidationErrorCode.FILE_TOO_LARGE]: 'File is too large',
  
  // Citizenship/Country validation errors
  [ValidationErrorCode.CITIZENSHIP_INVALID]: 'Invalid citizenship',
  [ValidationErrorCode.CITIZENSHIP_REQUIRED]: 'Citizenship is required',
  
  // Generic validation errors
  [ValidationErrorCode.FIELD_REQUIRED]: 'Field is required',
  [ValidationErrorCode.FIELD_INVALID]: 'Field is invalid',
  [ValidationErrorCode.VALIDATION_FAILED]: 'Validation failed',
};

/**
 * Field-specific validation error code mappings
 * Maps field names to their specific validation error codes
 */
export const FieldValidationErrorCodes: Record<string, Record<string, ValidationErrorCode>> = {
  email: {
    'invalid_string': ValidationErrorCode.EMAIL_INVALID_FORMAT,
    'too_small': ValidationErrorCode.EMAIL_REQUIRED,
    'required': ValidationErrorCode.EMAIL_REQUIRED,
  },
  password: {
    'too_small': ValidationErrorCode.PASSWORD_TOO_SHORT,
    'too_big': ValidationErrorCode.PASSWORD_TOO_LONG,
    'required': ValidationErrorCode.PASSWORD_REQUIRED,
    'invalid_string': ValidationErrorCode.PASSWORD_INVALID_FORMAT,
  },
  phoneNumber: {
    'too_small': ValidationErrorCode.PHONE_TOO_SHORT,
    'too_big': ValidationErrorCode.PHONE_TOO_LONG,
    'required': ValidationErrorCode.PHONE_REQUIRED,
    'invalid_string': ValidationErrorCode.PHONE_INVALID_FORMAT,
    'custom': ValidationErrorCode.PHONE_INVALID_COUNTRY_CODE,
  },
  firstName: {
    'required': ValidationErrorCode.STRING_REQUIRED,
    'too_small': ValidationErrorCode.STRING_TOO_SHORT,
    'too_big': ValidationErrorCode.STRING_TOO_LONG,
    'invalid_string': ValidationErrorCode.STRING_INVALID_FORMAT,
  },
  lastName: {
    'required': ValidationErrorCode.STRING_REQUIRED,
    'too_small': ValidationErrorCode.STRING_TOO_SHORT,
    'too_big': ValidationErrorCode.STRING_TOO_LONG,
    'invalid_string': ValidationErrorCode.STRING_INVALID_FORMAT,
  },
  citizenship: {
    'required': ValidationErrorCode.CITIZENSHIP_REQUIRED,
    'invalid_enum_value': ValidationErrorCode.CITIZENSHIP_INVALID,
  },
};
