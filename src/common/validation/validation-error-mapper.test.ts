import { z } from 'zod';
import { ValidationErrorMapper } from './validation-error-mapper';
import { ValidationErrorCode } from './validation-error-codes';

describe('ValidationErrorMapper', () => {
  describe('mapZodError', () => {
    it('should map email validation errors correctly', () => {
      const schema = z.object({
        email: z.string().email(),
      });

      try {
        schema.parse({ email: 'invalid-email' });
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        
        expect(mappedErrors.validationErrors.email).toBeDefined();
        expect(mappedErrors.validationErrors.email[0].code).toBe(ValidationErrorCode.EMAIL_INVALID_FORMAT);
        expect(mappedErrors.validationErrors.email[0].field).toBe('email');
        expect(mappedErrors.errorCount).toBe(1);
      }
    });

    it('should map password validation errors correctly', () => {
      const schema = z.object({
        password: z.string().min(8),
      });

      try {
        schema.parse({ password: 'abc' });
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        
        expect(mappedErrors.validationErrors.password).toBeDefined();
        expect(mappedErrors.validationErrors.password[0].code).toBe(ValidationErrorCode.PASSWORD_TOO_SHORT);
        expect(mappedErrors.validationErrors.password[0].field).toBe('password');
        expect(mappedErrors.validationErrors.password[0].value).toBe('abc');
      }
    });

    it('should map phone number validation errors correctly', () => {
      const schema = z.object({
        phoneNumber: z.string().min(8).max(15),
      });

      try {
        schema.parse({ phoneNumber: '123' });
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        
        expect(mappedErrors.validationErrors.phoneNumber).toBeDefined();
        expect(mappedErrors.validationErrors.phoneNumber[0].code).toBe(ValidationErrorCode.PHONE_TOO_SHORT);
      }
    });

    it('should map required field errors correctly', () => {
      const schema = z.object({
        firstName: z.string(),
        lastName: z.string(),
      });

      try {
        schema.parse({});
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        
        expect(mappedErrors.validationErrors.firstName).toBeDefined();
        expect(mappedErrors.validationErrors.lastName).toBeDefined();
        expect(mappedErrors.validationErrors.firstName[0].code).toBe(ValidationErrorCode.STRING_REQUIRED);
        expect(mappedErrors.validationErrors.lastName[0].code).toBe(ValidationErrorCode.STRING_REQUIRED);
        expect(mappedErrors.errorCount).toBe(2);
      }
    });

    it('should convert to legacy format correctly', () => {
      const schema = z.object({
        email: z.string().email(),
        password: z.string().min(8),
      });

      try {
        schema.parse({ email: 'invalid', password: 'abc' });
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        const legacyFormat = ValidationErrorMapper.toLegacyFormat(mappedErrors);
        
        expect(legacyFormat.email).toBeDefined();
        expect(legacyFormat.password).toBeDefined();
        expect(Array.isArray(legacyFormat.email)).toBe(true);
        expect(Array.isArray(legacyFormat.password)).toBe(true);
        expect(typeof legacyFormat.email[0]).toBe('string');
        expect(typeof legacyFormat.password[0]).toBe('string');
      }
    });

    it('should handle multiple errors for the same field', () => {
      const schema = z.object({
        email: z.string().email().min(5),
      });

      try {
        schema.parse({ email: 'a' });
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        
        expect(mappedErrors.validationErrors.email).toBeDefined();
        expect(mappedErrors.validationErrors.email.length).toBeGreaterThan(0);
      }
    });

    it('should handle nested object validation errors', () => {
      const schema = z.object({
        user: z.object({
          email: z.string().email(),
          profile: z.object({
            firstName: z.string(),
          }),
        }),
      });

      try {
        schema.parse({ 
          user: { 
            email: 'invalid', 
            profile: { firstName: '' } 
          } 
        });
      } catch (error) {
        const mappedErrors = ValidationErrorMapper.mapZodError(error as any);
        
        expect(mappedErrors.validationErrors['user.email']).toBeDefined();
        expect(mappedErrors.validationErrors['user.profile.firstName']).toBeDefined();
      }
    });
  });
});
