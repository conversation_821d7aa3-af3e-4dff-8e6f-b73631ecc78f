import { SetMetadata } from '@nestjs/common';

import { SubscriptionResource } from '@/common/enums/subscription.enum';

export interface RegistrationSubscriptionResourceMetadata {
  resource: SubscriptionResource;
}

export const REGISTRATION_SUBSCRIPTION_RESOURCE_KEY =
  'registrationSubscriptionResource';

export const CheckRegistrationResourceLimit = (
  resource: SubscriptionResource,
) => SetMetadata(REGISTRATION_SUBSCRIPTION_RESOURCE_KEY, { resource });

export const CheckRegistrationWorkersLimit = () =>
  CheckRegistrationResourceLimit(SubscriptionResource.Workers);
export const CheckRegistrationManagersLimit = () =>
  CheckRegistrationResourceLimit(SubscriptionResource.Managers);
