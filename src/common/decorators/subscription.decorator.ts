import { SetMetadata } from '@nestjs/common';

import {
  SubscriptionFeature,
  SubscriptionResource,
} from '@/common/enums/subscription.enum';

export interface SubscriptionFeatureMetadata {
  feature: SubscriptionFeature;
}

export interface SubscriptionResourceMetadata {
  resource: SubscriptionResource;
  currentCount?: number;
}

export const SUBSCRIPTION_FEATURE_KEY = 'subscriptionFeature';
export const SUBSCRIPTION_RESOURCE_KEY = 'subscriptionResource';

/**
 * Decorator to require a specific subscription feature
 * @param feature The subscription feature required
 */
export const RequireFeature = (feature: SubscriptionFeature) =>
  SetMetadata(SUBSCRIPTION_FEATURE_KEY, { feature });

/**
 * Decorator to check resource limits
 * @param resource The resource type to check
 * @param currentCount Optional current count (if not provided, will be fetched)
 */
export const CheckResourceLimit = (
  resource: SubscriptionResource,
  currentCount?: number,
) => SetMetadata(SUBSCRIPTION_RESOURCE_KEY, { resource, currentCount });

export const RequirePhotoConfirmation = () =>
  RequireFeature(SubscriptionFeature.PhotoConfirmation);
// export const RequireFinancialStatistics = () => RequireFeature(SubscriptionFeature.FinancialStatistics);
// export const RequireCharts = () => RequireFeature(SubscriptionFeature.Charts);
// export const RequireAdvancedAnalytics = () => RequireFeature(SubscriptionFeature.AdvancedAnalytics);
// export const RequireReportGeneration = () => RequireFeature(SubscriptionFeature.ReportGeneration);

export const CheckWorkersLimit = (currentCount?: number) =>
  CheckResourceLimit(SubscriptionResource.Workers, currentCount);
export const CheckProjectsLimit = (currentCount?: number) =>
  CheckResourceLimit(SubscriptionResource.Projects, currentCount);
export const CheckManagersLimit = (currentCount?: number) =>
  CheckResourceLimit(SubscriptionResource.Managers, currentCount);
