import { createParamDecorator } from '@nestjs/common';
import { Request } from 'express';

import { InvalidUserEntityException } from '../exceptions/custom-exceptions';

export const User = createParamDecorator((_data, ctx) => {
  const request: Request = ctx.switchToHttp().getRequest();
  const user = request.user;

  if (!user?.entityId && user?.role !== 'worker') {
    throw new InvalidUserEntityException();
  }

  return user;
});
