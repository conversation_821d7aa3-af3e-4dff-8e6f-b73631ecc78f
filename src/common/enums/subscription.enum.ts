export enum SubscriptionTier {
  Free = 'free',
  SmallBusiness = 'small_business',
  Medium = 'medium',
  Enterprise = 'enterprise',
}

export interface SubscriptionLimits {
  workers: number | 'unlimited';
  projects: number | 'unlimited';
  managers: number | 'unlimited';
  onlineSupport: boolean;
  webVersion: boolean;
  photoConfirmation: boolean;
  financialStatistics: boolean;
  charts: boolean;
  advancedAnalytics: boolean;
  reportGeneration: boolean;
}

export const SUBSCRIPTION_LIMITS: Record<SubscriptionTier, SubscriptionLimits> = {
  [SubscriptionTier.Free]: {
    workers: 3,
    projects: 1,
    managers: 0,
    onlineSupport: true,
    webVersion: false,
    photoConfirmation: false,
    financialStatistics: false,
    charts: false,
    advancedAnalytics: false,
    reportGeneration: false,
  },
  [SubscriptionTier.SmallBusiness]: {
    workers: 15,
    projects: 3,
    managers: 2,
    onlineSupport: true,
    webVersion: true,
    photoConfirmation: true,
    financialStatistics: false,
    charts: false,
    advancedAnalytics: false,
    reportGeneration: false,
  },
  [SubscriptionTier.Medium]: {
    workers: 50,
    projects: 10,
    managers: 5,
    onlineSupport: true,
    webVersion: true,
    photoConfirmation: true,
    financialStatistics: true,
    charts: true,
    advancedAnalytics: true,
    reportGeneration: true,
  },
  [SubscriptionTier.Enterprise]: {
    workers: 'unlimited',
    projects: 'unlimited',
    managers: 'unlimited',
    onlineSupport: true,
    webVersion: true,
    photoConfirmation: true,
    financialStatistics: true,
    charts: true,
    advancedAnalytics: true,
    reportGeneration: true,
  },
};

export enum SubscriptionFeature {
  OnlineSupport = 'onlineSupport',
  WebVersion = 'webVersion',
  PhotoConfirmation = 'photoConfirmation',
  FinancialStatistics = 'financialStatistics',
  Charts = 'charts',
  AdvancedAnalytics = 'advancedAnalytics',
  ReportGeneration = 'reportGeneration',
}

export enum SubscriptionResource {
  Workers = 'workers',
  Projects = 'projects',
  Managers = 'managers',
}
