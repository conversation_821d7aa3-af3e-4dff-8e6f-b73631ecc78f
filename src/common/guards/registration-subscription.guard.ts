import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

import { SubscriptionResource } from '@/common/enums/subscription.enum';
import { SubscriptionService } from '@/modules/subscription/subscription.service';
import { RegistrationCodesService } from '@/modules/registration-codes/registration-codes.service';

import {
  REGISTRATION_SUBSCRIPTION_RESOURCE_KEY,
  RegistrationSubscriptionResourceMetadata,
} from '../decorators/registration-subscription.decorator';

@Injectable()
export class RegistrationSubscriptionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly subscriptionService: SubscriptionService,
    private readonly registrationCodesService: RegistrationCodesService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const resourceMetadata =
      this.reflector.getAllAndOverride<RegistrationSubscriptionResourceMetadata>(
        REGISTRATION_SUBSCRIPTION_RESOURCE_KEY,
        [context.getHandler(), context.getClass()],
      );

    if (!resourceMetadata) {
      return true;
    }

    const request: Request = context.switchToHttp().getRequest();
    const { body, params } = request;

    const registrationCode = body?.registrationCode || params?.registrationCode;
    if (!registrationCode) {
      return true;
    }

    const codeCheck = await this.registrationCodesService.check({
      code: registrationCode,
      role:
        resourceMetadata.resource === SubscriptionResource.Workers
          ? 'worker'
          : 'manager',
    });

    if (!codeCheck.valid) {
      return true;
    }

    const partnerId = codeCheck.data.partnerId;

    await this.subscriptionService.enforceResourceLimit(
      partnerId,
      resourceMetadata.resource,
    );

    return true;
  }
}
