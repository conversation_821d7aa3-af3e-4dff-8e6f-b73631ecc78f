import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Request } from 'express';

import { SubscriptionResource } from '@/common/enums/subscription.enum';
import { SubscriptionService } from '@/modules/subscription/subscription.service';
import { RegistrationRequestsService } from '@/modules/registration-requests/registration-requests.service';
import { PartnersService } from '@/modules/partners/partners.service';
import { ManagersService } from '@/modules/managers/managers.service';

@Injectable()
export class RegistrationApprovalSubscriptionGuard implements CanActivate {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly registrationRequestsService: RegistrationRequestsService,
    private readonly partnersService: PartnersService,
    private readonly managersService: ManagersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: Request = context.switchToHttp().getRequest();
    const { params, user, body } = request;

    if (body?.status !== 'approved') {
      return true;
    }

    const requestId = params?.id;
    if (!requestId || !user) {
      return true;
    }

    const registrationRequest =
      await this.registrationRequestsService.findOne(requestId);
    if (!registrationRequest) {
      return true;
    }

    let partnerId: string | null | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager' && user.entityId) {
      partnerId = await this.managersService.getPartnerId(user.entityId);
      if (!partnerId) return false;
    } else {
      return false;
    }

    if (registrationRequest.workerId && partnerId) {
      await this.subscriptionService.enforceResourceLimit(
        partnerId,
        SubscriptionResource.Workers,
      );
    } else if (registrationRequest.managerId && partnerId) {
      await this.subscriptionService.enforceResourceLimit(
        partnerId,
        SubscriptionResource.Managers,
      );
    }

    return true;
  }
}
