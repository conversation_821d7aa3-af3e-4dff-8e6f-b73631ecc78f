import { Injectable, Logger } from '@nestjs/common';

import { securityNotificationEmailTemplate } from '@/common/emails/security-notification.email';
import { EmailService } from '../email/email.service';

export type SecurityNotificationType =
  | 'password_changed'
  | 'email_changed'
  | 'phone_changed'
  | 'profile_updated'
  | 'login_from_new_device'
  | 'login_from_new_location';

@Injectable()
export class SecurityNotificationsService {
  private readonly logger = new Logger(SecurityNotificationsService.name);

  constructor(private readonly emailService: EmailService) {}

  async sendSecurityNotification(
    email: string,
    type: SecurityNotificationType,
    metadata?: Record<string, any>,
  ) {
    try {
      const { subject, action, details, contactSupport } =
        this.getNotificationContent(type, metadata);

      await this.emailService.sendMail({
        to: email,
        subject,
        html: securityNotificationEmailTemplate(
          action,
          details,
          contactSupport,
        ),
      });

      this.logger.log(`Security notification of type ${type} sent to ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send security notification to ${email}`,
        error,
      );
      throw error;
    }
  }

  private getNotificationContent(
    type: SecurityNotificationType,
    metadata?: Record<string, any>,
  ) {
    const date = new Date().toLocaleString();
    const ip = metadata?.ip || 'unknown IP';
    const device = metadata?.device || 'unknown device';
    const location = metadata?.location || 'unknown location';

    switch (type) {
      case 'password_changed':
        return {
          subject: 'Security Alert: Your Password Was Changed',
          action: 'your password was changed',
          details: `This change was made on ${date} from ${ip} (${location}).
            If you made this change, you can disregard this email. If you didn't make this change, 
            please secure your account immediately by resetting your password.`,
          contactSupport:
            'If you need assistance, please contact our support team immediately.',
        };

      case 'email_changed':
        return {
          subject: 'Security Alert: Your Email Address Was Changed',
          action: 'your email address was changed',
          details: `Your email was changed from ${metadata?.oldEmail || 'your previous email'} to ${metadata?.newEmail || 'a new email address'}.
            This change was made on ${date} from ${ip} (${location}).
            If you made this change, you can disregard this email. If you didn't make this change, 
            please contact our support team immediately.`,
          contactSupport:
            'For security reasons, we send this notification to both your old and new email addresses.',
        };

      case 'phone_changed':
        return {
          subject: 'Security Alert: Your Phone Number Was Changed',
          action: 'your phone number was changed',
          details: `Your phone number was updated on ${date} from ${ip} (${location}).
            If you made this change, you can disregard this email. If you didn't make this change, 
            please secure your account immediately.`,
          contactSupport:
            'If you need assistance, please contact our support team immediately.',
        };

      case 'profile_updated':
        return {
          subject: 'Security Alert: Your Profile Information Was Updated',
          action: 'your profile information was updated',
          details: `Changes were made to your profile on ${date} from ${ip} (${location}).
            If you made these changes, you can disregard this email. If you didn't make these changes, 
            please review your account and contact support if needed.`,
          contactSupport: null,
        };

      case 'login_from_new_device':
        return {
          subject: 'Security Alert: New Device Login',
          action: 'we detected a login from a new device',
          details: `Someone signed into your account from a new device (${device}) on ${date} from ${location}.
            If this was you, you can disregard this email. If you don't recognize this activity, 
            please secure your account immediately by changing your password.`,
          contactSupport:
            'If you need assistance, please contact our support team immediately.',
        };

      case 'login_from_new_location':
        return {
          subject: 'Security Alert: Login from New Location',
          action: 'we detected a login from a new location',
          details: `Someone signed into your account from ${location} on ${date} using ${device}.
            If this was you, you can disregard this email. If you don't recognize this activity, 
            please secure your account immediately by changing your password.`,
          contactSupport:
            'If you need assistance, please contact our support team immediately.',
        };

      default:
        return {
          subject: 'Security Alert: Account Activity',
          action: 'there was activity on your account',
          details: `There was activity on your account on ${date} from ${ip} (${location}).
            If you recognize this activity, you can disregard this email. If you don't recognize this activity, 
            please secure your account immediately.`,
          contactSupport:
            'If you need assistance, please contact our support team immediately.',
        };
    }
  }
}
