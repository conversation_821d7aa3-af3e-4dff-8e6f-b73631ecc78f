import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { dailyReports } from '@/modules/db/entities/daily-report.entity';
import { users } from '@/modules/db/entities/user.entity';
import { workers } from '@/modules/db/entities/worker.entity';

export const getWorkerSchema = createSelectSchema(workers);

export const getWorkerWithUserSchema = createSelectSchema(workers)
  .pick({
    partnerId: true,
    approvalState: true,
    workingStatus: true,
    employmentStatus: true,
    profession: true,
    projectId: true,
  })
  .extend({
    companyName: z.string().nullable(),
    companyAvatarId: z.string().nullable(),
    user: createSelectSchema(users).omit({
      id: true,
      hashedPassword: true,
      documentScan: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      role: true,
    }),
  });

export const getWorkersWithBasicUserAndReportSchema = createSelectSchema(
  workers,
)
  .pick({
    id: true,
    workingStatus: true,
    validatedPresenceStatus: true,
    profession: true,
    projectId: true,
    createdAt: true,
    checkedPresenceAt: true,
    endEmploymentDate: true,
    employmentStatus: true,
  })
  .extend({
    user: createSelectSchema(users).pick({
      id: true,
      firstName: true,
      lastName: true,
      avatarId: true,
      phoneNumber: true,
      email: true,
    }),
    lastReport: createSelectSchema(dailyReports)
      .pick({
        id: true,
        reportDate: true,
        submittedHours: true,
        approvedHours: true,
        status: true,
        isManual: true,
        createdAt: true,
      })
      .nullable(),
  });

export const getWorkersWithUserAndReportSchema = getWorkerWithUserSchema.extend(
  {
    lastReport: createSelectSchema(dailyReports).nullable(),
  },
);

export const workersEmptyPresenceStatusSchema = z.object({
  count: z.number(),
  ids: z.array(z.string()),
});

export class WorkerDto extends createZodDto(getWorkerSchema) {}

export class WorkerWithUserDto extends createZodDto(getWorkerWithUserSchema) {}

export class WorkersWithBasicUserAndReportDto extends createZodDto(
  getWorkersWithBasicUserAndReportSchema,
) {}

export class WorkersEmptyPresenceStatusDto extends createZodDto(
  workersEmptyPresenceStatusSchema,
) {}
