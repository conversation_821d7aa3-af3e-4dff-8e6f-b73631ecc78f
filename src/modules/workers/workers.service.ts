import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import {
  and,
  desc,
  eq,
  inArray,
  isNotNull,
  isNull,
  like,
  lt,
  ne,
  or,
} from 'drizzle-orm';

import { Role } from '@/common/enums';
import {
  InvalidRegistrationCodeException,
  InvalidWorkerIdException,
  ManagerNotFoundException,
  PartnerNotFoundException,
  WorkerAlreadyEmployedException,
  WorkerAlreadyTerminatedException,
  WorkerNotFoundException,
  WorkerOnNoticeException,
} from '@/common/exceptions/custom-exceptions';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';

import { ActivityHistoryService } from '../activity-history/activity-history.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { Database } from '../db/db.module';
import { dailyReports } from '../db/entities/daily-report.entity';
import { users } from '../db/entities/user.entity';
import { workers } from '../db/entities/worker.entity';
import { ManagersService } from '../managers/managers.service';
import { NotificationsService } from '../notifications/notifications.service';
import { RegistrationCodesService } from '../registration-codes/registration-codes.service';
import { RegistrationRequestsService } from '../registration-requests/registration-requests.service';
import { CreateWorkerDto } from './dto/create-worker.dto';
import { UpdateWorkerDto } from './dto/update-worker.dto';
import { WorkerFilterParamsDto } from './dto/worker-filter-params.dto';
import { UpdatesGateway } from '../updates/updates.gateway';
import { UpdatesEvents } from '../updates/updates.types';
import { UsersService } from '../users/users.service';

@Injectable()
export class WorkersService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => RegistrationCodesService))
    private readonly registrationCodesService: Forwarded<RegistrationCodesService>,
    @Inject(forwardRef(() => RegistrationRequestsService))
    private readonly registrationRequestsService: Forwarded<RegistrationRequestsService>,
    @Inject(forwardRef(() => ActivityHistoryService))
    private readonly activityHistoryService: Forwarded<ActivityHistoryService>,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
    @Inject(forwardRef(() => UpdatesGateway))
    private readonly updatesGateway: Forwarded<UpdatesGateway>,
    @Inject(forwardRef(() => NotificationsService))
    private readonly notificationsService: NotificationsService,
    @Inject(forwardRef(() => ManagersService))
    private readonly managerService: Forwarded<ManagersService>,
  ) {}

  async create(createWorkerDto: CreateWorkerDto) {
    return (
      await this.db
        .insert(workers)
        .values(createWorkerDto)
        .returning({ id: workers.id })
    )[0];
  }

  async findAll(
    user: Omit<RequestUserType, 'sessionId'>,
    filterParams?: WorkerFilterParamsDto,
  ) {
    const whereClauses = this.generateFilterClauses(filterParams);
    let partnerId: string | undefined;
    let managedProjectsIds: string[] | undefined;
    let managerPermissionType: ManagerPermissionType | undefined = undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === Role.Manager) {
      const manager = await this.managerService.findOne(user.entityId);
      if (!manager) {
        throw new ManagerNotFoundException();
      }
      const managedProjects = await this.managerService.getManagedProjects(
        user.entityId,
      );
      managerPermissionType = manager.permissionType as ManagerPermissionType;
      managedProjectsIds = managedProjects.map((project) => project.id);
      partnerId = manager.partnerId;
    }
    if (!partnerId) {
      throw new PartnerNotFoundException();
    }

    const workersList = await this.db
      .select({
        id: workers.id,
        workingStatus: workers.workingStatus,
        validatedPresenceStatus: workers.validatedPresenceStatus,
        profession: workers.profession,
        projectId: workers.projectId,
        createdAt: workers.createdAt,
        checkedPresenceAt: workers.checkedPresenceAt,
        endEmploymentDate: workers.endEmploymentDate,
        employmentStatus: workers.employmentStatus,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          avatarId: users.avatarId,
          phoneNumber: users.phoneNumber,
          email: users.email,
        },
      })
      .from(workers)
      .where(
        and(
          ...whereClauses,
          eq(workers.partnerId, partnerId),
          eq(workers.approvalState, 'approved'),
          or(
            eq(workers.employmentStatus, 'active'),
            eq(workers.employmentStatus, 'quit_notice'),
            eq(workers.employmentStatus, 'terminated_notice'),
          ),
          managerPermissionType === ManagerPermissionType.ProjectManager &&
            managedProjectsIds
            ? inArray(workers.projectId, managedProjectsIds)
            : undefined,
        ),
      )
      .innerJoin(users, eq(users.id, workers.userId));

    const workersWithReports = await this.db.query.workers.findMany({
      columns: {
        id: true,
      },
      where: (workers, { or, eq }) =>
        or(...workersList.map((worker) => eq(workers.id, worker.id))),
      with: {
        dailyReports: {
          columns: {
            id: true,
            reportDate: true,
            submittedHours: true,
            approvedHours: true,
            status: true,
            isManual: true,
            createdAt: true,
          },
          orderBy: desc(dailyReports.createdAt),
          limit: 1,
        },
      },
    });

    return workersList.map((worker) => {
      const workerWithReports = workersWithReports.find(
        (workerWithReports) => workerWithReports.id === worker.id,
      );
      return {
        ...worker,
        lastReport: workerWithReports?.dailyReports[0] || null,
      };
    });
  }

  async findAllByProjectId(
    projectId: string,
    filterParams?: WorkerFilterParamsDto,
  ) {
    const whereClauses = this.generateFilterClauses(filterParams);
    const workersList = await this.db
      .select({
        id: workers.id,
        workingStatus: workers.workingStatus,
        validatedPresenceStatus: workers.validatedPresenceStatus,
        employmentStatus: workers.employmentStatus,
        endEmploymentDate: workers.endEmploymentDate,
        profession: workers.profession,
        projectId: workers.projectId,
        createdAt: workers.createdAt,
        checkedPresenceAt: workers.checkedPresenceAt,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          avatarId: users.avatarId,
          createdAt: users.createdAt,
          phoneNumber: users.phoneNumber,
          email: users.email,
        },
      })
      .from(workers)
      .where(
        and(
          ...whereClauses,
          eq(workers.projectId, projectId),
          eq(workers.approvalState, 'approved'),
          or(
            eq(workers.employmentStatus, 'active'),
            eq(workers.employmentStatus, 'quit_notice'),
            eq(workers.employmentStatus, 'terminated_notice'),
          ),
        ),
      )
      .innerJoin(users, eq(users.id, workers.userId));

    const workersWithReports = await this.db.query.workers.findMany({
      columns: {
        id: true,
      },
      where: (workers, { or, eq }) =>
        or(...workersList.map((worker) => eq(workers.id, worker.id))),
      with: {
        dailyReports: {
          columns: {
            id: true,
            reportDate: true,
            submittedHours: true,
            approvedHours: true,
            status: true,
            isManual: true,
            createdAt: true,
          },
          orderBy: desc(dailyReports.createdAt),
          limit: 1,
        },
      },
    });

    return workersList.map((worker) => {
      const workerWithReports = workersWithReports.find(
        (workerWithReports) => workerWithReports.id === worker.id,
      );
      return {
        ...worker,
        lastReport: workerWithReports?.dailyReports[0] || null,
      };
    });
  }

  findOne(id: string) {
    return this.db.query.workers.findFirst({
      where: (workers, { eq }) => eq(workers.id, id),
      with: {
        partner: {
          columns: {
            id: true,
          },
          with: {
            user: {
              columns: {
                id: true,
              },
            },
          },
        },
      },
    });
  }

  findOneByUserId(userId: string) {
    return this.db.query.workers.findFirst({
      where: (workers, { and, eq, or }) =>
        and(
          eq(workers.userId, userId),
          or(
            eq(workers.employmentStatus, 'active'),
            eq(workers.employmentStatus, 'quit_notice'),
            eq(workers.employmentStatus, 'terminated_notice'),
          ),
        ),
      with: {
        partner: {
          columns: {
            id: true,
          },
          with: {
            user: {
              columns: {
                id: true,
              },
            },
          },
        },
      },
    });
  }

  findOneWithBasicUser(id: string) {
    return this.db.query.workers.findFirst({
      where: eq(workers.id, id),
      with: {
        user: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            avatarId: true,
            createdAt: true,
            phoneNumber: true,
          },
        },
      },
    });
  }

  async findOneWithUser(id: string, user: RequestUserType) {
    let partnerId: string | undefined =
      user.role === 'partner' ? user.entityId : undefined;
    if (user.role === 'manager') {
      partnerId = await this.managerService.getPartnerId(user.entityId);
    }
    const worker = await this.db.query.workers.findFirst({
      where: and(
        eq(workers.id, id),
        partnerId ? eq(workers.partnerId, partnerId) : undefined,
      ),
      with: {
        user: {
          columns: {
            id: false,
            hashedPassword: false,
            documentScan: false,
            isEmailVerified: false,
            isPhoneVerified: false,
            role: false,
          },
        },
        partner: {
          columns: {
            companyName: true,
          },
          with: {
            user: {
              columns: {
                avatarId: true,
              },
            },
          },
        },
      },
    });

    if (!worker) throw new WorkerNotFoundException();

    const { partner, ...rest } = worker;

    return {
      ...rest,
      companyName: partner.companyName,
      companyAvatarId: partner.user.avatarId,
    };
  }

  async findOneWithPartner(id: string) {
    const worker = await this.db.query.workers.findFirst({
      where: eq(workers.id, id),
      with: {
        user: {
          columns: {
            id: false,
            hashedPassword: false,
            documentScan: false,
            isEmailVerified: false,
            isPhoneVerified: false,
            role: false,
          },
        },
        partner: {
          with: {
            user: true,
          },
        },
      },
    });

    if (!worker) throw new WorkerNotFoundException();

    const { partner, ...rest } = worker;

    return {
      ...rest,
      companyName: partner.companyName,
      companyAvatarId: partner.user.avatarId,
      partnerUserId: partner.user.id,
    };
  }

  update(id: string, updateWorkerDto: UpdateWorkerDto) {
    return this.db.transaction(async (tx) => {
      const worker = await tx.query.workers.findFirst({
        where: (workers, { eq }) => eq(workers.id, id),
      });

      if (!worker) {
        throw new WorkerNotFoundException();
      }

      await tx.update(workers).set(updateWorkerDto).where(eq(workers.id, id));

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.WORKER_UPDATED,
        payload: {
          workerId: id,
          projectId: worker.projectId,
        },
      });

      return worker;
    });
  }

  async changeEmploymentStatus(
    id: string,
    employmentStatus: 'active' | 'inactive' | 'quit' | 'terminated',
  ) {
    await this.db
      .update(workers)
      .set({ employmentStatus })
      .where(eq(workers.id, id));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.WORKER_EMPLOYMENT_CHANGED,
      payload: {
        workerId: id,
        status: employmentStatus,
      },
    });
  }

  async changeApprovalState(
    ids: string | string[],
    approvalState: 'approved' | 'rejected' | 'pending',
  ) {
    const workerIds = Array.isArray(ids) ? ids : [ids];

    await this.db
      .update(workers)
      .set({ approvalState })
      .where(inArray(workers.id, workerIds));

    for (const workerId of workerIds) {
      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.WORKER_APPROVAL_CHANGED,
        payload: {
          workerId,
          status: approvalState,
        },
      });
    }
  }

  async endEmployment(
    id: string,
    employmentStatus: 'quit' | 'terminated',
    operationAuthorId: string,
    endDate: Date = new Date(),
    reason?: string,
  ) {
    const worker = await this.findOneWithPartner(id);
    if (!worker) throw new WorkerNotFoundException();
    if (['quit', 'terminated'].includes(worker.employmentStatus)) {
      throw new WorkerAlreadyTerminatedException();
    } else if (
      ['quit_notice', 'terminated_notice'].includes(worker.employmentStatus)
    ) {
      throw new WorkerOnNoticeException();
    }

    const twoWeeksFromNow = new Date();
    twoWeeksFromNow.setDate(twoWeeksFromNow.getDate() + 14);

    const effectiveEndDate =
      endDate > twoWeeksFromNow ? endDate : twoWeeksFromNow;

    await this.db
      .update(workers)
      .set({
        employmentStatus: `${employmentStatus}_notice` as
          | 'quit_notice'
          | 'terminated_notice',
        endEmploymentDate: effectiveEndDate,
        endEmploymentReason: reason || null,
      })
      .where(eq(workers.id, id));

    if (worker.approvalState !== 'pending') {
      await this.activityHistoryService.createEmploymentActivity(
        `worker_${employmentStatus}_notice` as
          | 'worker_quit_notice'
          | 'worker_terminated_notice',
        operationAuthorId,
        worker.partnerId,
        id,
        undefined,
        {
          reason: reason || null,
          effectiveEndDate: effectiveEndDate.toISOString(),
        },
      );
    }

    if (employmentStatus === 'quit') {
      await this.notificationsService.send({
        title: 'Worker Resignation Notice',
        body: `${worker.user.firstName} ${worker.user.lastName} has submitted their resignation`,
        receiverUserId: worker.partnerUserId,
        topic: 'worker-quit',
        data: {
          workerId: worker.id,
          effectiveEndDate: effectiveEndDate.toISOString(),
        },
      });
    } else {
      await this.notificationsService.send({
        title: 'Employment Termination Notice',
        body: 'Your employment contract is being terminated',
        receiverUserId: worker.userId,
        topic: 'worker-terminated',
        data: {
          effectiveEndDate: effectiveEndDate.toISOString(),
          reason: reason || 'No reason provided',
        },
      });
    }

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.WORKER_EMPLOYMENT_CHANGED,
      payload: {
        workerId: id,
        status: `${employmentStatus}_notice`,
        effectiveEndDate,
      },
    });
  }

  @Cron(CronExpression.EVERY_HOUR)
  async processEndingEmployments() {
    try {
      const now = new Date();

      const workersToEnd = await this.db.query.workers.findMany({
        where: and(
          or(
            eq(workers.employmentStatus, 'quit_notice'),
            eq(workers.employmentStatus, 'terminated_notice'),
          ),
          lt(workers.endEmploymentDate, now),
        ),
        with: {
          user: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          partner: {
            columns: {
              id: true,
              companyName: true,
              userId: true,
            },
          },
        },
      });

      for (const worker of workersToEnd) {
        const finalStatus =
          worker.employmentStatus === 'quit_notice' ? 'quit' : 'terminated';

        await this.db
          .update(workers)
          .set({
            employmentStatus: finalStatus,
          })
          .where(eq(workers.id, worker.id));

        await this.activityHistoryService.createEmploymentActivity(
          `worker_${finalStatus}` as 'worker_quit' | 'worker_terminated',
          worker.partner.userId,
          worker.partnerId,
          worker.id,
          undefined,
          {
            finalizedAt: new Date().toISOString(),
          },
        );

        await this.registrationRequestsService.deleteAllPendingForWorker(
          worker.id,
        );

        const entityId = await this.usersService.getEntityIdFromDB(
          worker.userId,
        );
        await this.usersService.setEntityId(worker.userId, entityId);

        await this.notificationsService.send({
          title: 'Employment Ended',
          body: `Your employment with ${worker.partner.companyName} has ended`,
          receiverUserId: worker.userId,
          topic: 'worker-terminated',
          data: {
            status: finalStatus,
          },
        });

        await this.notificationsService.send({
          title: 'Worker Employment Ended',
          body: `${worker.user.firstName} ${worker.user.lastName}'s employment has ended`,
          receiverUserId: worker.partner.userId,
          topic: 'worker-terminated',
          data: {
            workerId: worker.id,
            status: finalStatus,
          },
        });

        await this.updatesGateway.sendMessage({
          type: UpdatesEvents.WORKER_EMPLOYMENT_CHANGED,
          payload: {
            workerId: worker.id,
            status: finalStatus,
          },
        });

        console.log(
          `Completed employment end process for worker ${worker.id} with status ${finalStatus}`,
        );
      }
    } catch (error) {
      console.error('Failed to process ending employments', error);
    }
  }

  async changeEmployer(
    userId: string,
    registrationCode: string,
    previousWorkerId?: string,
  ) {
    if (previousWorkerId) {
      const previousWorker = await this.findOne(previousWorkerId);
      if (!previousWorker) throw new InvalidWorkerIdException();
      if (
        ['active', 'quit_notice', 'terminated_notice'].includes(
          previousWorker.employmentStatus,
        )
      )
        throw new WorkerAlreadyEmployedException();
    }

    const registrationCodeCheck = await this.registrationCodesService.check({
      code: registrationCode,
      role: 'worker',
    });

    if (!registrationCodeCheck.valid) {
      throw new InvalidRegistrationCodeException();
    }

    const worker = await this.create({
      userId,
      managerId: registrationCodeCheck.data.managerId,
      partnerId: registrationCodeCheck.data.partnerId,
      profession: registrationCodeCheck.data.profession,
    });

    await this.registrationRequestsService.create({
      registrationCodeId: registrationCodeCheck.data.id,
      requestedAt: new Date(),
      workerId: worker.id,
    });

    await this.usersService.setEntityId(userId, worker.id);
  }

  async assignToProject(
    id: string,
    projectId: string,
    operationAuthorId?: string,
  ) {
    const worker = await this.findOne(id);
    if (!worker) throw new WorkerNotFoundException();

    await this.db.update(workers).set({ projectId }).where(eq(workers.id, id));

    if (operationAuthorId) {
      await this.activityHistoryService.createProjectActivity(
        'worker_assigned_to_project',
        operationAuthorId,
        worker.partnerId,
        projectId,
        id,
      );
    }

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.WORKER_UPDATED,
      payload: {
        workerId: id,
        projectId,
        action: 'project_assigned',
      },
    });
  }

  async unassignFromProject(
    id: string,
    projectId: string,
    operationAuthorId?: string,
  ) {
    const worker = await this.findOne(id);
    if (!worker) throw new WorkerNotFoundException();

    await this.db
      .update(workers)
      .set({ projectId: null })
      .where(and(eq(workers.id, id), eq(workers.projectId, projectId)));

    if (operationAuthorId) {
      await this.activityHistoryService.createProjectActivity(
        'worker_unassigned_from_project',
        operationAuthorId,
        worker.partnerId,
        projectId,
        id,
      );
    }

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.WORKER_UPDATED,
      payload: {
        workerId: id,
        projectId,
        action: 'project_unassigned',
      },
    });
  }

  async unassignAllFromProject(projectId: string) {
    const workersToUnassign = await this.db.query.workers.findMany({
      columns: { id: true },
      where: eq(workers.projectId, projectId),
    });

    await this.db
      .update(workers)
      .set({ projectId: null })
      .where(eq(workers.projectId, projectId));

    for (const worker of workersToUnassign) {
      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.WORKER_UPDATED,
        payload: {
          workerId: worker.id,
          projectId,
          action: 'project_unassigned',
        },
      });
      this.notificationsService.send({
        title: 'Project Unassigned',
        body: 'You have been unassigned from a project',
        receiverUserId: worker.id,
        topic: 'project-unassigned',
      });
    }
  }

  async changeWorkingStatus(
    id: string,
    workingStatus: 'started' | 'finished' | 'paused' | 'passive',
  ) {
    await this.db
      .update(workers)
      .set({ workingStatus })
      .where(eq(workers.id, id));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.WORKER_STATUS_CHANGED,
      payload: {
        workerId: id,
        status: workingStatus,
      },
    });
  }

  async changePresenceValidationStatus(
    id: string,
    presenceValidationStatus: 'validated' | 'pending' | 'late' | 'empty',
  ) {
    await this.db
      .update(workers)
      .set({ validatedPresenceStatus: presenceValidationStatus })
      .where(eq(workers.id, id));

    const worker = await this.findOne(id);
    if (!worker) throw new WorkerNotFoundException();

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.WORKER_PRESENCE_CHANGED,
      payload: {
        workerId: id,
        projectId: worker.projectId,
        status: presenceValidationStatus,
      },
    });
  }

  updateLastPresenceCheck(id: string) {
    return this.db
      .update(workers)
      .set({ checkedPresenceAt: new Date() })
      .where(eq(workers.id, id));
  }

  private generateFilterClauses(filterParams?: WorkerFilterParamsDto) {
    return [
      filterParams?.firstName
        ? or(
            like(users.firstName, filterParams.firstName),
            like(users.lastName, filterParams.firstName),
          )
        : undefined,
      filterParams?.lastName
        ? or(
            like(users.firstName, filterParams.lastName),
            like(users.lastName, filterParams.lastName),
          )
        : undefined,
      filterParams?.fullName
        ? or(
            like(users.firstName, `%${filterParams.fullName?.[0]}%`),
            like(users.lastName, `%${filterParams.fullName?.[0]}%`),
            filterParams.fullName?.[1]
              ? like(users.firstName, `%${filterParams.fullName?.[1]}%`)
              : undefined,
            filterParams.fullName?.[1]
              ? like(users.lastName, `%${filterParams.fullName?.[1]}%`)
              : undefined,
          )
        : undefined,
      filterParams?.projects
        ? or(
            ...filterParams.projects.includes.map((id) =>
              eq(workers.projectId, id),
            ),
            ...filterParams.projects.excludes.map((id) =>
              ne(workers.projectId, id),
            ),
          )
        : undefined,
      filterParams?.withoutProject ? isNull(workers.projectId) : undefined,
      filterParams?.withProject ? isNotNull(workers.projectId) : undefined,
      filterParams?.status === 'on-duty'
        ? or(
            eq(workers.workingStatus, 'started'),
            eq(workers.workingStatus, 'paused'),
          )
        : undefined,
      filterParams?.status === 'present'
        ? and(
            or(
              eq(workers.validatedPresenceStatus, 'validated'),
              eq(workers.validatedPresenceStatus, 'pending'),
            ),
            or(
              eq(workers.workingStatus, 'started'),
              eq(workers.workingStatus, 'paused'),
            ),
          )
        : undefined,
      filterParams?.status === 'absent'
        ? and(
            or(
              eq(workers.validatedPresenceStatus, 'empty'),
              eq(workers.validatedPresenceStatus, 'late'),
            ),
            or(
              eq(workers.workingStatus, 'started'),
              eq(workers.workingStatus, 'paused'),
            ),
          )
        : undefined,
      filterParams?.status === 'pending'
        ? eq(workers.validatedPresenceStatus, 'pending')
        : undefined,
    ];
  }

  async delete(id: string) {
    await this.db.delete(workers).where(eq(workers.id, id));
  }

  async getPartnerId(workerId: string) {
    const worker = await this.findOne(workerId);
    if (!worker) throw new WorkerNotFoundException();
    return worker.partnerId;
  }
}
