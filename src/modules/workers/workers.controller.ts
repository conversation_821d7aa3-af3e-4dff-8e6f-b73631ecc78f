import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  forwardRef,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { CanManage } from '@/common/decorators/management.decorator';
import { Roles } from '@/common/decorators/roles.decorator';
import { CheckWorkersLimit } from '@/common/decorators/subscription.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  ManagerNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
  WorkerNotFoundErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import {
  ManagementGuard,
  ResourceType,
} from '@/common/guards/management.guard';
import { SubscriptionGuard } from '@/common/guards/subscription.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { RolesGuard } from '@/common/guards/roles.guard';
import { Forwarded } from '@/common/types';
import { hashPassword } from '@/common/utils/hash';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { DailyReportsService } from '../daily-reports/daily-reports.service';
import { CreatePartnersWorkerDto } from '../partners/dto/create-partners-worker.dto';
import { FireWorkerDto } from '../partners/dto/fire-worker.dto';
import { UsersService } from '../users/users.service';
import { UpdateWorkerDto } from './dto/update-worker.dto';
import { WorkerFilterParamsDto } from './dto/worker-filter-params.dto';
import {
  WorkerWithUserDto,
  WorkersWithBasicUserAndReportDto,
} from './dto/worker.dto';
import { WorkersService } from './workers.service';
import { DailyReportWithPhotosDto } from '../daily-reports/dto/daily-report.dto';
import { ReportFilterParamsDto } from '../daily-reports/dto/daily-reports-filter-params.dto';
import { PresenceValidationsService } from '../presence-validations/presence-validations.service';
import { PresenceValidationDto } from '../presence-validations/dto/presence-validation.dto';

@ApiTags('Workers')
@ApiBearerAuth()
@Controller('workers')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WorkersController {
  constructor(
    private readonly workersService: WorkersService,
    @Inject(forwardRef(() => DailyReportsService))
    private readonly dailyReportsService: Forwarded<DailyReportsService>,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
    @Inject(forwardRef(() => PresenceValidationsService))
    private readonly presenceValidationsService: Forwarded<PresenceValidationsService>,
  ) {}

  @Get()
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'List all workers',
    description:
      'Retrieve a list of workers with optional filtering capabilities',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workers retrieved successfully',
    type: [WorkersWithBasicUserAndReportDto],
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access workers',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Manager or Partner not found',
    type: ManagerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: UnauthorizedErrorResponseDto,
  })
  findAll(
    @User() user: RequestUserType,
    @Query() filterParams?: WorkerFilterParamsDto,
  ): Promise<WorkersWithBasicUserAndReportDto[]> {
    return this.workersService.findAll(user, filterParams);
  }

  @Get(':workerId')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker by ID',
    description: 'Retrieve detailed information about a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker retrieved successfully',
    type: WorkerWithUserDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Access forbidden',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: UnauthorizedErrorResponseDto,
  })
  findOne(
    @Param('workerId') workerId: string,
    @User() user: RequestUserType,
  ): Promise<WorkerWithUserDto> {
    return this.workersService.findOneWithUser(workerId, user);
  }

  @Get(':workerId/daily-reports')
  @Roles(Role.Worker, Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker daily reports',
    description: 'Retrieve all daily reports for a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiQuery({ type: ReportFilterParamsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reports retrieved successfully',
    type: [DailyReportWithPhotosDto],
  })
  findAllByWorkerId(
    @Param('workerId') workerId: string,
    @Query() filterParams: ReportFilterParamsDto,
  ): Promise<DailyReportWithPhotosDto[]> {
    return this.dailyReportsService.findAllByWorkerId(workerId, filterParams);
  }

  @Get(':workerId/daily-reports/approved-hours/:reportDate')
  @Roles(Role.Worker, Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker approved hours',
    description:
      'Calculate total approved hours for a worker on a specific date',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiParam({
    name: 'reportDate',
    description: 'Date in YYYY-MM-DD format',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Approved hours calculated successfully',
    type: Number,
  })
  findAllApprovedHoursByWorkerId(
    @Param('workerId') workerId: string,
    @Param('reportDate') reportDate: string,
  ): Promise<number> {
    return this.dailyReportsService.calculateApprovedHoursForWorker(
      workerId,
      reportDate,
    );
  }

  @Patch(':workerId')
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Worker)
  @ApiOperation({
    summary: 'Update worker information',
    description:
      'Update information for a worker (consolidated from partners and managers controllers)',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker information updated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update this worker',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error',
    type: ValidationErrorResponseDto,
  })
  updateWorkerInfo(
    @Param('workerId') workerId: string,
    @Body() updateWorkerDto: UpdateWorkerDto,
  ) {
    return this.workersService.update(workerId, updateWorkerDto);
  }

  @Delete(':workerId/employment')
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Worker)
  @ApiOperation({
    summary: 'Terminate worker employment',
    description:
      'Terminate employment of a worker (consolidated from partners and managers controllers)',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker employment terminated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to terminate this worker',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or worker already terminated',
    type: BadRequestErrorResponseDto,
  })
  terminateEmployment(
    @User() user: RequestUserType,
    @Param('workerId') workerId: string,
    @Body() fireWorkerDto: FireWorkerDto,
  ) {
    return this.workersService.endEmployment(
      workerId,
      'terminated',
      user.id,
      fireWorkerDto.endDate,
      fireWorkerDto.reason,
    );
  }

  @Post()
  @UseGuards(SubscriptionGuard)
  @CheckWorkersLimit()
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Create new worker',
    description:
      'Create a new worker account (consolidated from partners controller)',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Worker created successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to create workers',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or email already exists',
    type: ValidationErrorResponseDto,
  })
  async createWorker(
    @Body() createWorkerDto: CreatePartnersWorkerDto,
    @User() user: RequestUserType,
  ) {
    const createdBaseUser = await this.usersService.create({
      ...createWorkerDto.userData,
      role: 'worker',
      hashedPassword: await hashPassword(createWorkerDto.userData.password),
    });

    return this.workersService.create({
      ...createWorkerDto.workerData,
      userId: createdBaseUser.id,
      partnerId: user.entityId,
    });
  }

  @Get(':workerId/presence-validations')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker presence validations',
    description: 'Retrieve all presence validations for a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Presence validations retrieved successfully',
    type: [PresenceValidationDto],
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access these validations',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  getPresenceValidations(
    @Param('workerId') workerId: string,
    @User() user: RequestUserType,
  ): Promise<PresenceValidationDto[]> {
    return this.presenceValidationsService.findAllForWorker(
      workerId,
      false,
      user,
    );
  }
}
