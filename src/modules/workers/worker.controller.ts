import {
  Controller,
  Get,
  HttpStatus,
  Inject,
  Param,
  Patch,
  UseGuards,
  forwardRef,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { CheckRegistrationWorkersLimit } from '@/common/decorators/registration-subscription.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
  WorkerNotFoundErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { WorkerNotFoundException } from '@/common/exceptions/custom-exceptions';
import { RolesGuard } from '@/common/guards/roles.guard';
import { RegistrationSubscriptionGuard } from '@/common/guards/registration-subscription.guard';
import { Forwarded } from '@/common/types';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { DailyReportsService } from '../daily-reports/daily-reports.service';
import { WorkerWithUserDto } from './dto/worker.dto';
import { WorkersValidTimeDto } from './dto/workers-valid-time.dto';
import { WorkersService } from './workers.service';

@ApiTags('Worker')
@ApiBearerAuth()
@Controller('worker')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WorkerController {
  constructor(
    private readonly workersService: WorkersService,
    @Inject(forwardRef(() => DailyReportsService))
    private readonly dailyReportsService: Forwarded<DailyReportsService>,
  ) {}

  @Get()
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Get current worker profile',
    description: 'Retrieve detailed information about the authenticated worker',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker profile retrieved successfully',
    type: WorkerWithUserDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access worker profile',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  async getCurrentWorker(
    @User() user: RequestUserType,
  ): Promise<WorkerWithUserDto> {
    const worker = await this.workersService.findOneWithUser(
      user.entityId,
      user,
    );
    if (!worker) throw new WorkerNotFoundException();
    return worker;
  }

  @Patch('quit')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Quit job',
    description: 'Worker voluntarily ends their employment',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employment ended successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to perform this action',
  })
  quitJob(@User() user: RequestUserType) {
    return this.workersService.endEmployment(user.entityId, 'quit', user.id);
  }

  @Patch('employer/:registrationCode')
  @UseGuards(RegistrationSubscriptionGuard)
  @CheckRegistrationWorkersLimit()
  @Roles(Role.Worker)
  @ApiOperation({
    summary: 'Change employer',
    description:
      'Worker changes their employer using a registration code (tip: use the "metadata" endpoint to update worker\'s employment information)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employer changed successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid registration code or worker already employed',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to change employer',
  })
  changeEmployer(
    @User() user: RequestUserType,
    @Param('registrationCode') registrationCode: string,
  ) {
    return this.workersService.changeEmployer(
      user.id,
      registrationCode,
      user.entityId,
    );
  }

  @Get('valid-time')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Get valid working time',
    description:
      'Returns the valid working time in seconds for the current day, accounting for pauses',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Valid working time retrieved successfully',
    type: WorkersValidTimeDto,
  })
  async getValidWorkingTime(
    @User() user: RequestUserType,
  ): Promise<WorkersValidTimeDto> {
    return this.dailyReportsService.calculateValidWorkingTime(user.entityId);
  }
}
