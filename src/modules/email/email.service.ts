import { Injectable, Logger } from '@nestjs/common';
import { Resend } from 'resend';

import env from '@/config/env.config';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly resend: Resend;
  private readonly from: string;

  constructor() {
    this.from = env.RESEND_FROM;
    this.resend = new Resend(env.RESEND_API_KEY);
  }

  async sendMail(options: {
    to: string;
    subject: string;
    html: string;
    from?: string;
  }) {
    try {
      const result = await this.resend.emails.send({
        from: options.from || this.from,
        to: options.to,
        subject: options.subject,
        html: options.html,
      });

      this.logger.log(
        `Email sent successfully to ${options.to}, ID: ${result.data?.id}`,
      );
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to send email to ${options.to}: ${errorMessage}`,
        error,
      );
      throw error;
    }
  }
}
