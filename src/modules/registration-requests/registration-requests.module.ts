import { Module, forwardRef } from '@nestjs/common';

import { RegistrationRequestController } from './registration-request.controller';
import { RegistrationRequestsController } from './registration-requests.controller';
import { RegistrationRequestsService } from './registration-requests.service';
import { ActivityHistoryModule } from '../activity-history/activity-history.module';
import { ManagersModule } from '../managers/managers.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';
import { PartnersModule } from '../partners/partners.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => WorkersModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => ActivityHistoryModule),
    forwardRef(() => RegistrationCodesModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => UpdatesModule),
  ],
  exports: [RegistrationRequestsService],
  providers: [RegistrationRequestsService],
  controllers: [RegistrationRequestsController, RegistrationRequestController],
})
export class RegistrationRequestsModule {}
