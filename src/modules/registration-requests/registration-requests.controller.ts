import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import {
  CheckWorkersLimit,
  CheckManagersLimit,
} from '@/common/decorators/subscription.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';
import { RegistrationApprovalSubscriptionGuard } from '@/common/guards/registration-approval-subscription.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

import { RegistrationRequestDto } from './dto/registration-request.dto';
import { RegistrationRequestStatusChangeDto } from './dto/registration-request-status-change.dto';
import { RegistrationRequestsService } from './registration-requests.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { WorkerFilterParamsDto } from '../workers/dto/worker-filter-params.dto';

@ApiTags('Registration Requests')
@ApiBearerAuth()
@Controller('registration-requests')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RegistrationRequestsController {
  constructor(
    private readonly registrationRequestsService: RegistrationRequestsService,
  ) {}

  @Get()
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'List registration requests',
    description:
      'Retrieve all registration requests managed by the authenticated user',
  })
  @ApiQuery({ type: WorkerFilterParamsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Registration requests retrieved successfully',
    type: [RegistrationRequestDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to view requests',
    type: ForbiddenErrorResponseDto,
  })
  getAll(
    @User() user: RequestUserType,
    @Query() filterParams?: WorkerFilterParamsDto,
  ): Promise<RegistrationRequestDto[]> {
    return this.registrationRequestsService.getAllForUser(user, filterParams);
  }

  @Patch(':id')
  @UseGuards(RegistrationApprovalSubscriptionGuard)
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'Change request status',
    description: 'Approve or reject a pending registration request',
  })
  @ApiParam({
    name: 'id',
    description: 'Registration request unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Request status changed successfully',
  })
  @ApiNotFoundResponse({ description: 'Request not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to change this request status',
  })
  @ApiBadRequestResponse({
    description: 'Invalid status value',
    type: BadRequestErrorResponseDto,
  })
  changeStatus(
    @User() user: RequestUserType,
    @Param('id') id: string,
    @Body() statusChangeDto: RegistrationRequestStatusChangeDto,
  ) {
    return this.registrationRequestsService.changeStatus(
      id,
      user,
      statusChangeDto.status,
    );
  }
}
