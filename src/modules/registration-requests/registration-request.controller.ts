import {
  Controller,
  Get,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  RegistrationRequestNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { WorkersRegistrationRequest } from './dto/registration-request.dto';
import { RegistrationRequestsService } from './registration-requests.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Worker\'s Registration Request')
@ApiBearerAuth()
@Controller('registration-request')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RegistrationRequestController {
  constructor(
    private readonly registrationRequestsService: RegistrationRequestsService,
  ) {}

  @Get()
  @Roles(Role.Worker)
  @ApiOperation({
    summary: 'Get current worker registration request',
    description:
      'Retrieve pending registration request for the authenticated worker',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Registration request retrieved successfully',
    type: WorkersRegistrationRequest,
  })
  @ApiNotFoundResponse({
    description: 'No pending request found',
    type: RegistrationRequestNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  getCurrentWorkerRequest(
    @User() user: RequestUserType,
  ): Promise<WorkersRegistrationRequest> {
    return this.registrationRequestsService.findPendingForWorker(user.entityId);
  }
}
