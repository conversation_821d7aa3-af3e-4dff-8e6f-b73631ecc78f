import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { roleInfoSchema } from '@/modules/auth/dto/request-user.dto';
import { users } from '@/modules/db/entities/user.entity';
import { managerWithProjectsSchema } from '@/modules/managers/dto/manager.dto';
import { getPartnerSchema } from '@/modules/partners/dto/partner.dto';
import { getWorkerSchema } from '@/modules/workers/dto/worker.dto';

export const getUserSchema = createSelectSchema(users).omit({
  id: true,
  hashedPassword: true,
  documentScan: true,
  isEmailVerified: true,
  isPhoneVerified: true,
  role: true,
});

export const userMetaDataSchema = z.object({
  roleInfo: roleInfoSchema,
  verifications: z.object({
    isEmailVerified: z.boolean(),
    isPhoneVerified: z.boolean(),
  }),
});

export class UserDto extends createZodDto(getUserSchema) {}

export const userWithRoleInfoSchema = getUserSchema.extend({
  partners: getPartnerSchema.array(),
  managers: managerWithProjectsSchema.array(),
  workers: getWorkerSchema.array(),
});

export class UserWithRoleInfoDto extends createZodDto(userWithRoleInfoSchema) {}

export class UserMetaDataDto extends createZodDto(userMetaDataSchema) {}
