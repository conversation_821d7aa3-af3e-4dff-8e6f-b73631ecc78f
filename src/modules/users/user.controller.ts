import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Patch,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { AllowUnverifiedEmail } from '@/common/decorators/allow-unverified-email.decorator';
import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
  UserNotFoundErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import {
  UpdatePersonalInfoDto,
  UpdateUserEmailDto,
} from './dto/update-user.dto';
import { UserDto, UserMetaDataDto } from './dto/user.dto';
import { UsersService } from './users.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { UserNotFoundException } from '@/common/exceptions/custom-exceptions';

@ApiTags('Current User')
@ApiBearerAuth()
@Controller('user')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @AllowUnverifiedEmail()
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Retrieves the profile information of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User profile retrieved successfully',
    type: UserDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  async getCurrentUser(@User() user: RequestUserType): Promise<UserDto> {
    const userData = await this.usersService.findOne(user.id);
    if (!userData) throw new UserNotFoundException();
    return userData;
  }

  @Delete()
  @ApiOperation({
    summary: 'Delete current user',
    description:
      'Delete the authenticated user (currently restricted to managers only)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User deleted successfully',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Access forbidden - only managers can delete users',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    type: UserNotFoundErrorResponseDto,
  })
  @UseGuards(RolesGuard)
  @Roles(Role.Manager) // NOTE: Currently restricted to managers only for safety
  async deleteCurrentUser(@User() user: RequestUserType): Promise<void> {
    return this.usersService.deleteUser(user);
  }

  @Get('metadata')
  @AllowUnverifiedEmail()
  @ApiOperation({
    summary: 'Get current user metadata',
    description:
      'Retrieves the metadata of the authenticated user, including role information and verification status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User metadata retrieved successfully',
    type: UserMetaDataDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  getCurrentUserMetadata(
    @User() user: RequestUserType,
  ): Promise<UserMetaDataDto> {
    return this.usersService.getUserMetaData(user.id);
  }

  @Patch()
  @ApiOperation({
    summary: 'Update current user profile',
    description: 'Update the profile information of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User profile updated successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
    type: ValidationErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User was not found',
    type: UserNotFoundErrorResponseDto,
  })
  updatePersonalInfo(
    @Body() updateUserDto: UpdatePersonalInfoDto,
    @User() user: RequestUserType,
  ): Promise<void> {
    return this.usersService.updatePersonalInfo(user.id, updateUserDto);
  }

  @Patch('email')
  @AllowUnverifiedEmail()
  @ApiOperation({
    summary: 'Update current user email',
    description: 'Update the email address of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User email updated successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or email already exists',
    type: ValidationErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User was not found',
    type: UserNotFoundErrorResponseDto,
  })
  updateEmail(
    @Body() updateEmailDto: UpdateUserEmailDto,
    @User() user: RequestUserType,
  ): Promise<void> {
    return this.usersService.updateEmail(user.id, updateEmailDto);
  }
}
