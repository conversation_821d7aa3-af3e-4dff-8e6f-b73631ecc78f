import {
  Controller,
  Get,
  HttpStatus,
  NotFoundException,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import {
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
  UserNotFoundErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { UserDto } from './dto/user.dto';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { UserNotFoundException } from '@/common/exceptions/custom-exceptions';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':id')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get user by ID',
    description:
      'Retrieve a specific user by their ID (restricted to partners and managers)',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the user',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User found and returned successfully',
    type: UserDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    type: UserNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Access forbidden - insufficient permissions',
    type: ForbiddenErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid user ID format',
    type: ValidationErrorResponseDto,
  })
  async findOne(@Param('id') id: string): Promise<UserDto> {
    const user = await this.usersService.findOne(id);
    if (!user) throw new UserNotFoundException();
    return user;
  }
}
