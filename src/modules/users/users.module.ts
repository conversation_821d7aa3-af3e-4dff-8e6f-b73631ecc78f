import { Module, forwardRef } from '@nestjs/common';

import { UserController } from './user.controller';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { CredentialVerificationModule } from '../credential-verification/credential-verification.module';
import { ManagersModule } from '../managers/managers.module';
import { SecurityNotificationsModule } from '../security-notifications/security-notifications.module';
import { UpdatesModule } from '../updates/updates.module';

@Module({
  imports: [
    forwardRef(() => CredentialVerificationModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => SecurityNotificationsModule),
    forwardRef(() => ManagersModule),
  ],
  controllers: [UsersController, UserController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
