import { Module, forwardRef } from '@nestjs/common';

import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { DailyReportsModule } from '../daily-reports/daily-reports.module';
import { ManagersModule } from '../managers/managers.module';
import { PartnersModule } from '../partners/partners.module';
import { SchedulesModule } from '../schedules/schedules.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';
import { PresenceValidationsModule } from '../presence-validations/presence-validations.module';

@Module({
  imports: [
    forwardRef(() => WorkersModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => SchedulesModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => UsersModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => PresenceValidationsModule),
    DailyReportsModule,
  ],
  controllers: [ProjectsController],
  providers: [ProjectsService],
  exports: [ProjectsService],
})
export class ProjectsModule {}
