import {
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { SQLWrapper, asc, desc, eq } from 'drizzle-orm';

import { Forwarded } from '@/common/types';

import { Database } from '../db/db.module';
import { projectManagers } from '../db/entities/project-manager.entity';
import { projects } from '../db/entities/project.entity';
import { ManagersService } from '../managers/managers.service';
import { CreateProjectDto } from './dto/create-projects.dto';
import { ProjectsFilterParamsDto } from './dto/projects-filter-params.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { UpdatesGateway } from '../updates/updates.gateway';
import { UpdatesEvents } from '../updates/updates.types';
import { WorkersService } from '../workers/workers.service';
import { ManagerNotFoundException, NotAuthorizedException, PartnerNotFoundException, ProjectNotFoundException } from '@/common/exceptions/custom-exceptions';

@Injectable()
export class ProjectsService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => UpdatesGateway))
    private readonly updatesGateway: Forwarded<UpdatesGateway>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
  ) {}

  async create(
    createProjectDto: CreateProjectDto,
    user: {
      id: string;
      entityId: string;
      role: string;
      permissionType?: string;
      [key: string]: any;
    },
  ) {
    let partnerId: string | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (!manager) throw new ManagerNotFoundException();
      if (manager.permissionType !== 'all') {
        throw new NotAuthorizedException();
      }
      partnerId = manager.partnerId;
    } else {
      throw new NotAuthorizedException();
    }
    if (!partnerId) {
      throw new PartnerNotFoundException();
    }
    const project = (
      await this.db
        .insert(projects)
        .values({
          ...createProjectDto,
          partnerId,
        })
        .returning({ id: projects.id })
    )[0];
    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.PROJECT_CREATED,
      payload: {
        projectId: project.id,
        partnerId: partnerId,
      },
    });
    return project;
  }

  async findAll(
    user: Omit<RequestUserType, 'sessionId'>,
    filterParams?: ProjectsFilterParamsDto,
  ) {
    let partnerId: string | undefined;
    let projectIds: string[] | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (!manager) throw new ManagerNotFoundException();
      partnerId = manager.partnerId;
      if (manager.permissionType === 'project_manager') {
        projectIds = manager.projectManagers.map((pm: any) => pm.projectId);
        if (!projectIds.length) return [];
      }
    }
    if (!partnerId) {
      throw new PartnerNotFoundException();
    }
    const { whereClauses, orderBy } = this.generateFilterClauses(filterParams);
    const projects = await this.db.query.projects.findMany({
      where: (projects, { eq, and, inArray }) => {
        let base = and(
          eq(projects.partnerId, partnerId),
          eq(projects.isActive, true),
          ...whereClauses,
        );
        if (projectIds) {
          base = and(base, inArray(projects.id, projectIds));
        }
        return base;
      },
      orderBy,
      columns: {
        id: true,
        name: true,
        address: true,
        createdAt: true,
      },
      with: {
        workers: {
          where: (workers, { eq, or }) =>
            or(
              eq(workers.employmentStatus, 'active'),
              eq(workers.employmentStatus, 'quit_notice'),
              eq(workers.employmentStatus, 'terminated_notice'),
            ),
          columns: {
            id: true,
          },
        },
      },
    });
    return projects.map(({ workers, ...rest }) => ({
      ...rest,
      teamSize: workers.length,
    }));
  }

  async findOne(id: string) {
    const project = await this.db.query.projects.findFirst({
      where: (projects, { eq, and }) =>
        and(eq(projects.id, id), eq(projects.isActive, true)),
      columns: {
        id: true,
        name: true,
        address: true,
        description: true,
        createdAt: true,
        partnerId: true,
      },
      with: {
        dailyReports: {
          columns: { id: true, status: true },
        },
        workers: {
          where: (workers, { eq, or }) =>
            or(
              eq(workers.employmentStatus, 'active'),
              eq(workers.employmentStatus, 'quit_notice'),
              eq(workers.employmentStatus, 'terminated_notice'),
            ),
          columns: { id: true },
        },
        projectManagers: {
          columns: { managerId: true },
        },
      },
    });
    if (!project) throw new ProjectNotFoundException();

    const { dailyReports, workers, projectManagers, ...rest } = project;

    let manager = null;
    if (projectManagers && projectManagers.length > 0) {
      const managerId = projectManagers[0].managerId;
      manager = await this.managersService.findOneWithBasicUser(managerId);
    }

    return {
      ...rest,
      teamSize: workers.length,
      reportCount: {
        total: dailyReports.length,
        submitted: dailyReports.filter(
          (report) => report.status === 'submitted',
        ).length,
        approved: dailyReports.filter((report) => report.status === 'approved')
          .length,
        declined: dailyReports.filter((report) => report.status === 'declined')
          .length,
        pending: dailyReports.filter((report) => report.status === 'pending')
          .length,
      },
      manager: manager || null,
    };
  }

  findOneWithOnDutyWorkers(id: string) {
    return this.db.query.projects.findFirst({
      where: (projects, { eq, and }) =>
        and(eq(projects.id, id), eq(projects.isActive, true)),
      with: {
        workers: {
          where: (workers, { eq, or }) =>
            or(
              eq(workers.workingStatus, 'started'),
              eq(workers.workingStatus, 'paused'),
            ),
          with: {
            user: {
              columns: {
                hashedPassword: false,
              },
            },
          },
        },
      },
    });
  }

  findOneWithPresentWorkers(id: string) {
    return this.db.query.projects.findFirst({
      where: (projects, { eq, and }) =>
        and(eq(projects.id, id), eq(projects.isActive, true)),
      with: {
        workers: {
          where: (workers, { eq, and, or }) =>
            and(
              eq(workers.validatedPresenceStatus, 'validated'),
              or(
                eq(workers.workingStatus, 'started'),
                eq(workers.workingStatus, 'paused'),
              ),
            ),
          with: {
            user: {
              columns: {
                hashedPassword: false,
              },
            },
          },
        },
      },
    });
  }

  findOneWithAbsentWorkers(id: string) {
    return this.db.query.projects.findFirst({
      where: (projects, { eq, and }) =>
        and(eq(projects.id, id), eq(projects.isActive, true)),
      with: {
        workers: {
          where: (workers, { eq, and, or }) =>
            and(
              or(
                eq(workers.validatedPresenceStatus, 'empty'),
                eq(workers.validatedPresenceStatus, 'late'),
              ),
              or(
                eq(workers.workingStatus, 'started'),
                eq(workers.workingStatus, 'paused'),
              ),
            ),
          with: {
            user: {
              columns: {
                hashedPassword: false,
              },
            },
          },
        },
      },
    });
  }

  async update(id: string, updateProjectDto: UpdateProjectDto) {
    return await this.db.transaction(async (tx) => {
      const project = await tx.query.projects.findFirst({
        where: (projects, { eq }) => eq(projects.id, id),
      });

      if (!project) {
        throw new ProjectNotFoundException();
      }

      const response = await tx
        .update(projects)
        .set(updateProjectDto)
        .where(eq(projects.id, id))
        .returning({
          id: projects.id,
        });

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.PROJECT_UPDATED,
        payload: {
          projectId: id,
          partnerId: project.partnerId,
        },
      });
      return response;
    });
  }

  async remove(id: string) {
    const project = await this.findOne(id);
    if (!project) {
      throw new ProjectNotFoundException();
    }

    await Promise.all([
      this.update(id, { isActive: false }),
      this.workersService.unassignAllFromProject(id),
      this.db.delete(projectManagers).where(eq(projectManagers.projectId, id)),
    ]);

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.PROJECT_DELETED,
      payload: {
        projectId: id,
        partnerId: project.partnerId,
      },
    });
  }

  async getProjectManagers(projectId: string) {
    const result = await this.db.query.projectManagers.findMany({
      where: (pm, { eq }) => eq(pm.projectId, projectId),
      with: {
        manager: {
          with: {
            user: {
              columns: {
                hashedPassword: false,
              },
            },
          },
        },
      },
    });

    return result.map((pm) => pm.manager);
  }

  async assignManagerToProjectExclusive(
    managerId: string,
    projectId: string,
    operationAuthorId?: string,
  ) {
    const currentManagers = await this.getProjectManagers(projectId);
  
    for (const manager of currentManagers) {
      if (manager.id !== managerId) {
        await this.managersService.removeManagerFromProject(
          manager.id,
          projectId,
          operationAuthorId,
          true,
        );
      }
    }
   
    await this.managersService.assignManagerToProject(
      managerId,
      projectId,
      operationAuthorId,
    );
   
    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT,
      payload: { managerId, projectId },
    });
    return { success: true };
  }

  async removeAllManagersFromProject(
    projectId: string,
    operationAuthorId?: string,
  ) {
    const currentManagers = await this.getProjectManagers(projectId);
    for (const manager of currentManagers) {
      await this.managersService.removeManagerFromProject(
        manager.id,
        projectId,
        operationAuthorId,
      );
      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT,
        payload: { managerId: manager.id, projectId },
      });
    }
    return { success: true };
  }

  private generateFilterClauses(filterParams?: ProjectsFilterParamsDto) {
    const whereClauses: SQLWrapper[] = [];

    const sortBy = filterParams?.sortBy
      ? filterParams.sortBy === 'date'
        ? projects.createdAt
        : filterParams.sortBy === 'name'
          ? projects.name
          : projects.createdAt
      : projects.createdAt;

    const orderBy =
      filterParams?.sortOrder && sortBy
        ? filterParams.sortOrder === 'asc'
          ? asc(sortBy)
          : desc(sortBy)
        : desc(projects.createdAt);

    return { whereClauses, orderBy };
  }
}
