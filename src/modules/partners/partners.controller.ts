import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpStatus,
  Inject,
  Patch,
  UseGuards,
  forwardRef,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  PartnerNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';
import { Forwarded } from '@/common/types';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { ManagersService } from '../managers/managers.service';
import { WorkersService } from '../workers/workers.service';
import { PartnerWithUserDto, LimitedPartnerWithUserDto } from './dto/partner.dto';
import { UpdatePartnerDto } from './dto/update-partner.dto';
import { PartnersService } from './partners.service';
import { ForbiddenRoleException, PartnerNotFoundException } from '@/common/exceptions/custom-exceptions';

@ApiTags('Partner')
@ApiBearerAuth()
@Controller('partner')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PartnersController {
  constructor(
    private readonly partnersService: PartnersService,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
  ) {}

  @Get()
  @Roles(Role.Partner, Role.Manager, Role.Worker)
  @ApiOperation({
    summary: 'Get partner information',
    description: 'Retrieve partner information based on user role. Partners get their own info, managers and workers get their partner\'s limited info',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Partner information retrieved successfully',
    type: PartnerWithUserDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this information',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Partner not found',
    type: PartnerNotFoundErrorResponseDto,
  })
  async getPartnerInfo(
    @User() user: RequestUserType,
  ): Promise<PartnerWithUserDto | LimitedPartnerWithUserDto> {
    let partnerId: string | undefined;

    switch (user.role) {
      case Role.Partner:
        partnerId = user.entityId;
        return this.partnersService.findOneWithUser(partnerId);

      case Role.Manager:
        partnerId = await this.managersService.getPartnerId(user.entityId);
        if (!partnerId) {
          throw new PartnerNotFoundException();
        }
        return this.partnersService.findLimitedPartnerInfo(partnerId);

      case Role.Worker:
        partnerId = await this.workersService.getPartnerId(user.entityId);
        if (!partnerId) {
          throw new PartnerNotFoundException();
        }
        return this.partnersService.findLimitedPartnerInfo(partnerId);

      default:
        throw new ForbiddenRoleException();
    }
  }

  @Patch()
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Update partner profile',
    description: 'Update personal information for the authenticated partner',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Partner profile updated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update this profile',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Partner was not found',
    type: PartnerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error',
    type: ValidationErrorResponseDto,
  })
  updatePersonalInfo(
    @Body() updatePartnerDto: UpdatePartnerDto,
    @User() user: RequestUserType,
  ): Promise<void> {
    return this.partnersService.update(user.entityId, updatePartnerDto);
  }
}
