import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { partners } from '@/modules/db/entities/partner.entity';
import { users } from '@/modules/db/entities/user.entity';

export const getPartnerSchema = createSelectSchema(partners);

export const getPartnerWithUserSchema = getPartnerSchema
  .pick({ 
    taxNumber: true,
    registrationAddress: true,
    companyName: true,
  })
  .extend({
    user: createSelectSchema(users).omit({
      id: true,
      hashedPassword: true,
      documentScan: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      role: true,
    }),
    workerCount: z.number(),
  });

export const getLimitedPartnerWithUserSchema = getPartnerSchema
  .pick({
    taxNumber: true,
    registrationAddress: true,
    companyName: true,
  })
  .extend({
    user: createSelectSchema(users).pick({
      firstName: true,
      lastName: true,
      phoneNumber: true,
      avatarId: true,
      email: true,
      createdAt: true,
    }),
    workerCount: z.number(),
  });

export class PartnerDto extends createZodDto(getPartnerSchema) {}

export class PartnerWithUserDto extends createZodDto(
  getPartnerWithUserSchema,
) {}

export class LimitedPartnerWithUserDto extends createZodDto(
  getLimitedPartnerWithUserSchema,
) {}
