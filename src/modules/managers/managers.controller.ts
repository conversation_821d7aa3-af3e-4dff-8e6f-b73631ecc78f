import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  forwardRef,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { CheckManagersLimit } from '@/common/decorators/subscription.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  ForbiddenErrorResponseDto,
  ManagerNotFoundErrorResponseDto,
  PartnerNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import {
  InsufficientManagerPermissionsException,
  ManagerNotFoundException,
  PartnerNotFoundException,
} from '@/common/exceptions/custom-exceptions';
import { RolesGuard } from '@/common/guards/roles.guard';
import { SubscriptionGuard } from '@/common/guards/subscription.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

import { Forwarded } from '@/common/types';
import { hashPassword } from '@/common/utils/hash';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { CreatePartnersManagerDto } from '../partners/dto/create-partners-manager.dto';
import { UsersService } from '../users/users.service';
import { ManagerFilterParamsDto } from './dto/manager-filter-params.dto';
import { ManagerWithBasicUserDto } from './dto/manager.dto';
import { ManagerWithUserDto } from './dto/manager.dto';
import { ManagersService } from './managers.service';

@ApiTags('Managers')
@ApiBearerAuth()
@Controller('managers')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ManagersController {
  constructor(
    private readonly managersService: ManagersService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
  ) {}

  @Get()
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'List all managers for the authenticated partner',
    description:
      'Retrieve a list of managers with optional filtering capabilities',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Managers retrieved successfully',
    type: [ManagerWithBasicUserDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Partner not found',
    type: PartnerNotFoundErrorResponseDto,
  })
  async findAll(
    @User() user: RequestUserType,
    @Query() filterParams: ManagerFilterParamsDto,
  ): Promise<ManagerWithBasicUserDto[]> {
    let partnerId: string | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      partnerId = await this.managersService.getPartnerId(user.entityId);
    }

    if (!partnerId) {
      throw new PartnerNotFoundException();
    }

    return this.managersService.findAllByPartner(partnerId, filterParams);
  }

  @Get(':managerId')
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'Get manager by ID',
    description:
      'Retrieve detailed information about a specific manager for the authenticated partner',
  })
  @ApiParam({
    name: 'managerId',
    description: 'Manager unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager retrieved successfully',
    type: ManagerWithUserDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Manager not found',
    type: ManagerNotFoundErrorResponseDto,
  })
  async findOne(
    @User() user: RequestUserType,
    @Param('managerId') managerId: string,
  ): Promise<ManagerWithUserDto> {
    let partnerId: string | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      partnerId = await this.managersService.getPartnerId(user.entityId);
    }

    const manager = await this.managersService.findOneWithUser(managerId);
    if (!manager) {
      throw new ManagerNotFoundException();
    }
    if (manager.partnerId !== partnerId) {
      throw new InsufficientManagerPermissionsException();
    }

    return manager;
  }

  @Patch(':managerId/terminate')
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  // NOTE: ManagementGuard logic is handled in the service for now
  @ApiOperation({
    summary: 'Terminate manager',
    description: 'Partner terminates a manager',
  })
  @ApiParam({
    name: 'managerId',
    description: 'Manager unique identifier',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager termination successful',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to terminate this manager',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Manager not found',
    type: ManagerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  async terminateEmployment(
    @User() user: RequestUserType,
    @Param('managerId') managerId: string,
  ): Promise<{ message: string }> {
    await this.managersService.terminateEmployment(managerId, user);
    return { message: 'Termination successful' };
  }

  @Patch(':managerId/permission')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Update manager permission type',
    description:
      'Change the permission type of a manager (consolidated from partners controller)',
  })
  @ApiParam({
    name: 'managerId',
    description: 'Manager unique identifier',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager permission updated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update manager permissions',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Manager not found',
    type: ManagerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  updateManagerPermission(
    @Param('managerId') managerId: string,
    @Body() { permissionType }: { permissionType: ManagerPermissionType },
  ): Promise<void> {
    return this.managersService.updatePermissionType(managerId, permissionType);
  }

  @Post()
  @UseGuards(SubscriptionGuard)
  @CheckManagersLimit()
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Create new manager',
    description:
      'Create a new manager account (consolidated from manager controller)',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Manager created successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to create managers',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or email already exists',
    type: ValidationErrorResponseDto,
  })
  async createManager(
    @Body() createManagerData: CreatePartnersManagerDto,
    @User() user: RequestUserType,
  ) {
    const { password, ...safeUserData } = createManagerData.userData;
    const createdBaseUser = await this.usersService.create({
      ...safeUserData,
      role: 'manager',
      hashedPassword: await hashPassword(password),
    });

    return this.managersService.create({
      userId: createdBaseUser.id,
      partnerId: user.entityId,
      permissionType: createManagerData.managerData
        .permissionType as ManagerPermissionType,
    });
  }
}
