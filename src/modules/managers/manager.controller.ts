import { Controller, Get, HttpStatus, Patch, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  ForbiddenErrorResponseDto,
  ManagerNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { ManagerNotFoundException } from '@/common/exceptions/custom-exceptions';
import { RolesGuard } from '@/common/guards/roles.guard';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { ManagerWithUserAndCompanyDto } from './dto/manager.dto';
import { ManagersService } from './managers.service';

@ApiTags('Manager')
@ApiBearerAuth()
@Controller('manager')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ManagerController {
  constructor(private readonly managersService: ManagersService) {}

  @Get()
  @Roles(Role.Manager)
  @ApiOperation({
    summary: 'Get current manager profile',
    description:
      'Retrieve detailed information about the authenticated manager',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager profile retrieved successfully',
    type: ManagerWithUserAndCompanyDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access manager profile',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Manager not found',
    type: ManagerNotFoundErrorResponseDto,
  })
  async getCurrentManager(
    @User() user: RequestUserType,
  ): Promise<ManagerWithUserAndCompanyDto> {
    const manager = await this.managersService.findOneWithUserAndCompany(
      user.entityId,
    );
    if (!manager) throw new ManagerNotFoundException();
    return manager;
  }

  @Get('projects')
  @Roles(Role.Manager)
  @ApiOperation({
    summary: 'Get managed projects',
    description: 'Retrieve all projects managed by the authenticated manager',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of managed projects retrieved successfully',
  })
  async getManagedProjects(@User() user: RequestUserType) {
    return this.managersService.getManagedProjects(user.entityId);
  }

  @Patch('quit')
  @Roles(Role.Manager)
  @ApiOperation({
    summary: 'Quit as manager',
    description: 'Manager voluntarily ends their employment',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employment quit successful',
  })
  async quitEmployment(@User() user: RequestUserType) {
    await this.managersService.quitEmployment(user.entityId, user.id);
    return { message: 'Quit successful' };
  }
}
