import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { SQL, and, eq, isNotNull, isNull, like, or } from 'drizzle-orm';

import { ApprovalState } from '@/common/enums';
import {
  InsufficientManagerPermissionsException,
  ManagerNotFoundException,
  OperationNotAllowedException,
  PartnerNotFoundException,
} from '@/common/exceptions/custom-exceptions';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';

import { Database } from '../db/db.module';
import { CreateManagerDto } from './dto/create-manager.dto';
import { ManagerFilterParamsDto } from './dto/manager-filter-params.dto';
import { ActivityHistoryService } from '../activity-history/activity-history.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { managers } from '../db/entities/manager.entity';
import { projectManagers } from '../db/entities/project-manager.entity';
import { users } from '../db/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { UpdatesGateway } from '../updates/updates.gateway';
import { UpdatesEvents } from '../updates/updates.types';

@Injectable()
export class ManagersService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => UpdatesGateway))
    private readonly updatesGateway: Forwarded<UpdatesGateway>,
    private readonly notificationsService: NotificationsService,
    @Inject(forwardRef(() => ActivityHistoryService))
    private readonly activityHistoryService: Forwarded<ActivityHistoryService>,
  ) {}

  async create(createManagerDto: CreateManagerDto) {
    return (
      await this.db
        .insert(managers)
        .values(createManagerDto)
        .returning({ id: managers.id })
    )[0];
  }

  findAll() {
    return this.db.query.managers.findMany({
      where: (managers, { eq, or }) =>
        or(
          eq(managers.employmentStatus, 'active'),
          eq(managers.employmentStatus, 'quit_notice'),
          eq(managers.employmentStatus, 'terminated_notice'),
        ),
    });
  }

  findOne(id: string) {
    return this.db.query.managers.findFirst({
      where: (managers, { eq, and, or }) =>
        and(
          eq(managers.id, id),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
        ),
    });
  }

  findOneWithPartner(id: string) {
    return this.db.query.managers.findFirst({
      where: (managers, { eq, and, or }) =>
        and(
          eq(managers.id, id),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
        ),
      with: {
        partner: true,
      },
    });
  }

  findOneWithUser(id: string) {
    return this.db.query.managers.findFirst({
      where: (managers, { eq, and, or }) =>
        and(
          eq(managers.id, id),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
        ),
      with: {
        user: {
          columns: {
            id: false,
            hashedPassword: false,
            documentScan: false,
            isEmailVerified: false,
            isPhoneVerified: false,
            role: false,
          },
        },
      },
    });
  }

  findOneWithBasicUser(id: string) {
    return this.db.query.managers.findFirst({
      where: (managers, { eq, and, or }) =>
        and(
          eq(managers.id, id),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
        ),
      with: {
        user: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            avatarId: true,
            phoneNumber: true,
            email: true,
          },
        },
      },
    });
  }
  findOneByUserId(userId: string) {
    return this.db.query.managers.findFirst({
      where: (managers, { eq, and, or }) =>
        and(
          eq(managers.userId, userId),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
        ),
      with: {
        partner: true,
        projectManagers: true,
      },
    });
  }

  async assignManagerToProject(
    managerId: string,
    projectId: string,
    operationAuthorId?: string,
  ) {
    const manager = await this.findOne(managerId);
    if (!manager) throw new ManagerNotFoundException();

    const result = (
      await this.db
        .insert(projectManagers)
        .values({
          managerId,
          projectId,
        })
        .onConflictDoNothing()
        .returning()
    )[0];

    if (result) {
      if (operationAuthorId) {
        await this.activityHistoryService.createProjectActivity(
          'manager_assigned_to_project',
          operationAuthorId,
          manager.partnerId,
          projectId,
          undefined,
          managerId,
        );
      }

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.PROJECT_MANAGER_UPDATED,
        payload: {
          projectId,
          managerId,
          action: 'assigned',
        },
      });
      this.notificationsService.send({
        title: 'Manager Assigned to Project',
        body: 'You have been assigned to the project',
        receiverUserId: manager.userId,
        topic: 'manager-assigned-to-project',
        data: { projectId },
      });
    }
  }

  async removeManagerFromProject(
    managerId: string,
    projectId: string,
    operationAuthorId?: string,
    deleteMissingManager?: boolean,
  ) {
  
    const manager = await this.findOne(managerId);
    if (!manager && !deleteMissingManager) throw new ManagerNotFoundException();
    if (!manager) {
      await this.db.delete(projectManagers).where(
        and(
          eq(projectManagers.managerId, managerId),
          eq(projectManagers.projectId, projectId),
        ),
      );
      return;
    };
    
    const result = await this.db
      .delete(projectManagers)
      .where(
        and(
          eq(projectManagers.managerId, managerId),
          eq(projectManagers.projectId, projectId),
        ),
      );

    if (result) {
      if (operationAuthorId) {
        await this.activityHistoryService.createProjectActivity(
          'manager_unassigned_from_project',
          operationAuthorId,
          manager.partnerId,
          projectId,
          undefined,
          managerId,
        );
      }

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.PROJECT_MANAGER_UPDATED,
        payload: {
          projectId,
          managerId,
          action: 'removed',
        },
      });
      this.notificationsService.send({
        title: 'Manager Removed from Project',
        body: 'You have been removed from the project',
        receiverUserId: manager.userId,
        topic: 'manager-removed-from-project',
        data: { projectId },
      });
    }
  }

  async getManagedProjects(managerId: string) {
    const result = await this.db.query.projectManagers.findMany({
      where: (pm, { eq }) => eq(pm.managerId, managerId),
      with: {
        project: true,
      },
    });

    return result.map((pm) => pm.project);
  }

  async updatePermissionType(
    managerId: string,
    permissionType: ManagerPermissionType,
  ) {
    const result = await this.db
      .update(managers)
      .set({ permissionType })
      .where(eq(managers.id, managerId));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.MANAGER_UPDATED,
      payload: {
        managerId,
        changes: {
          permissionType: true,
        },
      },
    });

    return result;
  }

  async changeApprovalState(
    id: string,
    approvalState: 'approved' | 'rejected' | 'pending',
  ) {
    const result = await this.db
      .update(managers)
      .set({ approvalState })
      .where(eq(managers.id, id));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.MANAGER_UPDATED,
      payload: {
        managerId: id,
        changes: {
          approvalState: true,
        },
      },
    });

    return result;
  }

  async getPartnerId(managerId: string) {
    const manager = await this.db.query.managers.findFirst({
      where: eq(managers.id, managerId),
    });

    return manager?.partnerId;
  }

  private generateFilterClauses(filterParams?: ManagerFilterParamsDto) {
    return [
      filterParams?.firstName
        ? or(
            like(users.firstName, `%${filterParams.firstName}%`),
            like(users.lastName, `%${filterParams.firstName}%`),
          )
        : undefined,
      filterParams?.lastName
        ? or(
            like(users.firstName, `%${filterParams.lastName}%`),
            like(users.lastName, `%${filterParams.lastName}%`),
          )
        : undefined,
      filterParams?.fullName
        ? or(
            like(users.firstName, `%${filterParams.fullName?.[0]}%`),
            like(users.lastName, `%${filterParams.fullName?.[0]}%`),
            filterParams.fullName?.[1]
              ? like(users.firstName, `%${filterParams.fullName?.[1]}%`)
              : undefined,
            filterParams.fullName?.[1]
              ? like(users.lastName, `%${filterParams.fullName?.[1]}%`)
              : undefined,
          )
        : undefined,
      filterParams?.permissionType
        ? eq(managers.permissionType, filterParams.permissionType)
        : undefined,
      filterParams?.approvalState
        ? eq(managers.approvalState, filterParams.approvalState)
        : undefined,
      filterParams?.isActive !== undefined
        ? and(
            eq(managers.approvalState, ApprovalState.Approved),
            or(
              eq(managers.employmentStatus, 'active'),
              eq(managers.employmentStatus, 'quit_notice'),
              eq(managers.employmentStatus, 'terminated_notice'),
            ),
          )
        : undefined,
    ];
  }

  async findAllByPartner(partnerId: string, filters?: ManagerFilterParamsDto) {
    const whereClauses = this.generateFilterClauses(filters);
    const managersList = await this.db
      .select({
        id: managers.id,
        approvalState: managers.approvalState,
        permissionType: managers.permissionType,
        partnerId: managers.partnerId,
        user: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          avatarId: users.avatarId,
          phoneNumber: users.phoneNumber,
          email: users.email,
        },
      })
      .from(managers)
      .where(
        and(
          eq(managers.partnerId, partnerId),
          eq(managers.approvalState, 'approved'),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
          ...whereClauses.filter(Boolean),
        ),
      )
      .innerJoin(users, eq(users.id, managers.userId));

    return managersList;
  }

  async findOneWithUserAndCompany(id: string) {
    const manager = await this.db.query.managers.findFirst({
      where: (managers, { eq, and, or }) =>
        and(
          eq(managers.id, id),
          or(
            eq(managers.employmentStatus, 'active'),
            eq(managers.employmentStatus, 'quit_notice'),
            eq(managers.employmentStatus, 'terminated_notice'),
          ),
        ),
      with: {
        user: {
          columns: {
            id: false,
            hashedPassword: false,
            documentScan: false,
            isEmailVerified: false,
            isPhoneVerified: false,
            role: false,
          },
        },
        partner: {
          columns: {
            companyName: true,
          },
          with: {
            user: {
              columns: {
                avatarId: true,
              },
            },
          },
        },
      },
    });
    if (!manager) return null;
    const { partner, ...rest } = manager;
    return {
      ...rest,
      companyName: partner?.companyName ?? null,
      companyAvatarId: partner?.user?.avatarId ?? null,
    };
  }

  async quitEmployment(
    managerId: string,
    userId: string,
    endDate?: Date,
    reason?: string,
  ) {
    const manager = await this.findOneWithPartner(managerId);
    if (!manager) throw new ManagerNotFoundException();
    const effectiveEndDate = endDate || new Date();
    await this.db.transaction(async (tx) => {
      await tx
        .update(managers)
        .set({
          employmentStatus: 'quit',
          endEmploymentDate: effectiveEndDate.toISOString(),
        })
        .where(eq(managers.id, managerId));

      await this.activityHistoryService.createEmploymentActivity(
        'manager_quit',
        userId,
        manager.partnerId,
        undefined,
        managerId,
        {
          reason: reason || null,
          effectiveEndDate: effectiveEndDate.toISOString(),
        },
        tx,
      );
    });
    await this.notificationsService.send({
      title: 'Manager Resignation',
      body: 'Your employment has been ended',
      receiverUserId: manager.userId,
      topic: 'manager-quit',
      data: { managerId, effectiveEndDate: effectiveEndDate.toISOString() },
    });
    await this.notificationsService.send({
      title: 'Manager Resignation',
      body: 'A manager has quit',
      receiverUserId: manager.partner.userId,
      topic: 'manager-quit',
      data: { managerId, effectiveEndDate: effectiveEndDate.toISOString() },
    });
    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED,
      payload: {
        managerId,
        status: 'quit',
        effectiveEndDate,
      },
    });
  }

  async terminateEmployment(
    managerId: string,
    user: RequestUserType,
    endDate?: Date,
    reason?: string,
  ) {
    let partnerId = user.role === 'partner' ? user.entityId : undefined;
    if (!partnerId) {
      partnerId = await this.getPartnerId(user.entityId);
    }

    if (!partnerId) throw new PartnerNotFoundException();

    const manager = await this.findOne(managerId);
    if (!manager) throw new ManagerNotFoundException();

    if (manager.partnerId !== partnerId)
      throw new InsufficientManagerPermissionsException();

    if (
      manager.permissionType === ManagerPermissionType.All &&
      user.role === 'manager'
    ) {
      throw new OperationNotAllowedException(
        'Manager with all permissions cannot be terminated by another manager',
      );
    }

    const effectiveEndDate = endDate || new Date();
    await this.db
      .update(managers)
      .set({
        employmentStatus: 'terminated',
        endEmploymentDate: effectiveEndDate.toISOString(),
      })
      .where(eq(managers.id, managerId));

    await this.activityHistoryService.createEmploymentActivity(
      'manager_terminated',
      user.id,
      partnerId,
      undefined,
      managerId,
      {
        reason: reason || null,
        effectiveEndDate: effectiveEndDate.toISOString(),
      },
    );

    await this.notificationsService.send({
      title: 'Manager Termination',
      body: 'Your employment contract has been terminated',
      receiverUserId: manager.userId,
      topic: 'manager-terminated',
      data: {
        effectiveEndDate: effectiveEndDate.toISOString(),
        reason: reason || 'No reason provided',
      },
    });
    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED,
      payload: {
        managerId,
        status: 'terminated',
        effectiveEndDate,
      },
    });
  }
}
