import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { RequirePhotoConfirmation } from '@/common/decorators/subscription.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';
import { SubscriptionGuard } from '@/common/guards/subscription.guard';

import { PresenceValidationDataDto } from './dto/presence-validation-data.dto';
import { PresenceValidationParamsWithWorkerIdsDto } from './dto/presence-validation-params.dto';
import { PresenceValidationDto } from './dto/presence-validation.dto';
import { PresenceValidationsService } from './presence-validations.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { PresenceValidationNotFoundException } from '@/common/exceptions/custom-exceptions';

@ApiTags('Presence Validations')
@ApiBearerAuth()
@Controller('presence-validations')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PresenceValidationsController {
  constructor(
    private readonly presenceValidationsService: PresenceValidationsService,
  ) {}

  @Post()
  @UseGuards(SubscriptionGuard)
  @RequirePhotoConfirmation()
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Create presence validations',
    description: 'Create presence validations for multiple workers',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Presence validations created successfully',
    type: [String],
  })
  @ApiBadRequestResponse({ description: 'Invalid validation parameters' })
  @ApiForbiddenResponse({ description: 'Not authorized to create validations' })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  create(
    @Body() presenceValidationParams: PresenceValidationParamsWithWorkerIdsDto,
    @User() user: RequestUserType,
  ): Promise<string[]> {
    return this.presenceValidationsService.createForWorkers(
      presenceValidationParams,
      user.id,
    );
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get validation by ID',
    description: 'Retrieve a specific presence validation',
  })
  @ApiParam({
    name: 'id',
    description: 'Presence validation unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Presence validation retrieved successfully',
    type: PresenceValidationDto,
  })
  @ApiNotFoundResponse({ description: 'Validation not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this validation',
  })
  async findOne(@Param('id') id: string): Promise<PresenceValidationDto> {
    const validation = await this.presenceValidationsService.findOne(id);
    if (!validation) throw new PresenceValidationNotFoundException();
    return validation;
  }

  @Patch(':id/data')
  @HttpCode(HttpStatus.OK)
  @Roles(Role.Worker)
  @ApiOperation({
    summary: 'Submit validation data',
    description: 'Submit data for a specific presence validation',
  })
  @ApiParam({
    name: 'id',
    description: 'Presence validation unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Validation data submitted successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid validation data' })
  @ApiNotFoundResponse({ description: 'Validation not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to submit validation data',
  })
  submitData(
    @Param('id') id: string,
    @Body() presenceValidationData: PresenceValidationDataDto,
  ) {
    return this.presenceValidationsService.setPresenceValidationData(
      id,
      presenceValidationData,
    );
  }
}
