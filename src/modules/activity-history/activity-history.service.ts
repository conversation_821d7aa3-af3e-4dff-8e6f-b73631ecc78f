import {
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { and, asc, desc, eq, isNotNull, isNull } from 'drizzle-orm';

import { Forwarded } from '@/common/types';

import { Database } from '../db/db.module';
import { ActivityHistoryFilterParamsDto } from './dto/activity-history-filter-params.dto';
import { CreateActivityHistoryDto } from './dto/create-activity-history.dto';
import { activityHistory } from '../db/entities/activity-history.entity';
import { ManagersService } from '../managers/managers.service';
import { ManagerNotFoundException, NotAuthorizedException, PartnerNotFoundException } from '@/common/exceptions/custom-exceptions';

@Injectable()
export class ActivityHistoryService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
  ) {}

  async create(
    createActivityHistoryDto: CreateActivityHistoryDto,
    tx?: Database,
  ) {
    const dbOrTx = tx || this.db;
    return (
      await dbOrTx
        .insert(activityHistory)
        .values(createActivityHistoryDto)
        .returning({ id: activityHistory.id })
    )[0];
  }

  async findAll(
    user: {
      id: string;
      entityId: string;
      role: string;
      permissionType?: string;
      [key: string]: any;
    },
    filterParams?: ActivityHistoryFilterParamsDto,
  ) {
    let partnerId: string | undefined;
    let projectIds: string[] | undefined;

    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (!manager) throw new ManagerNotFoundException();
      partnerId = manager.partnerId;

      if (manager.permissionType === 'project_manager') {
        projectIds = manager.projectManagers.map((pm: any) => pm.projectId);
        if (!projectIds.length) return [];
      }
    } else {
      throw new NotAuthorizedException();
    }

    if (!partnerId) {
      throw new PartnerNotFoundException();
    }

    const { whereClauses, orderBy } = this.generateFilterClauses(
      filterParams,
      projectIds,
    );
    const activities = await this.db.query.activityHistory.findMany({
      where: (activityHistory, { eq, and, inArray }) => {
        let baseCondition = and(
          eq(activityHistory.partnerId, partnerId),
          ...whereClauses,
        );

        if (projectIds && projectIds.length > 0) {
          baseCondition = and(
            baseCondition,
            inArray(activityHistory.projectId, projectIds),
          );
        }

        return baseCondition;
      },
      orderBy,
      columns: {
        id: true,
        activityType: true,
        happenedAt: true,
        metadata: true,
      },
      with: {
        worker: {
          with: {
            user: {
              columns: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        manager: {
          with: {
            user: {
              columns: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        project: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    return activities.map(({ worker, manager, project, ...activity }) => ({
      id: activity.id,
      activityType: activity.activityType,
      workerId: worker?.id,
      managerId: manager?.id,
      projectId: project?.id,
      projectName: project?.name,
      firstName: (worker?.user?.firstName ||
        manager?.user?.firstName) as string,
      lastName: (worker?.user?.lastName || manager?.user?.lastName) as string,
      role: worker?.id ? 'worker' : 'manager',
      happenedAt: activity.happenedAt,
      metadata: activity.metadata,
      email: (worker?.user?.email || manager?.user?.email) as string,
    }));
  }

  private generateFilterClauses(
    filterParams?: ActivityHistoryFilterParamsDto,
    projectIds?: string[],
  ) {
    const whereClauses = [
      filterParams?.activityType
        ? eq(activityHistory.activityType, filterParams.activityType)
        : undefined,
      filterParams?.projectId
        ? eq(activityHistory.projectId, filterParams.projectId)
        : undefined,
    ].filter(Boolean);

    if (filterParams?.role === 'worker') {
      whereClauses.push(isNotNull(activityHistory.workerId));
    } else if (filterParams?.role === 'manager') {
      whereClauses.push(isNotNull(activityHistory.managerId));
    }

    const sortBy = filterParams?.sortBy
      ? filterParams.sortBy === 'date'
        ? activityHistory.happenedAt
        : filterParams.sortBy === 'activityType'
          ? activityHistory.activityType
          : activityHistory.happenedAt
      : activityHistory.happenedAt;

    const orderBy =
      filterParams?.sortOrder && sortBy
        ? filterParams.sortOrder === 'asc'
          ? asc(sortBy)
          : desc(sortBy)
        : desc(activityHistory.happenedAt);

    return { whereClauses, orderBy };
  }

  async createEmploymentActivity(
    activityType:
      | 'worker_hired'
      | 'worker_quit'
      | 'worker_terminated'
      | 'worker_quit_notice'
      | 'worker_terminated_notice'
      | 'manager_hired'
      | 'manager_quit'
      | 'manager_terminated'
      | 'manager_quit_notice'
      | 'manager_terminated_notice',
    operationAuthorId: string,
    partnerId: string,
    workerId?: string,
    managerId?: string,
    metadata?: any,
    tx?: Database,
  ) {
    return this.create(
      {
        activityType,
        operationAuthorId,
        partnerId,
        workerId,
        managerId,
        metadata: metadata ? JSON.stringify(metadata) : undefined,
      },
      tx,
    );
  }

  async createProjectActivity(
    activityType:
      | 'worker_assigned_to_project'
      | 'worker_unassigned_from_project'
      | 'manager_assigned_to_project'
      | 'manager_unassigned_from_project',
    operationAuthorId: string,
    partnerId: string,
    projectId: string,
    workerId?: string,
    managerId?: string,
    metadata?: any,
    tx?: Database,
  ) {
    return this.create(
      {
        activityType,
        operationAuthorId,
        partnerId,
        projectId,
        workerId,
        managerId,
        metadata: metadata ? JSON.stringify(metadata) : undefined,
      },
      tx,
    );
  }
}
