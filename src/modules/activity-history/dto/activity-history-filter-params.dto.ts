import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const activityHistoryFilterParamsSchema = z.object({
  sortBy: z.enum(['date', 'activityType']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  activityType: z
    .enum([
      // Employment activities
      'worker_hired',
      'worker_quit',
      'worker_terminated',
      'worker_quit_notice',
      'worker_terminated_notice',
      'manager_hired',
      'manager_quit',
      'manager_terminated',
      'manager_quit_notice',
      'manager_terminated_notice',
      // Project assignment activities
      'worker_assigned_to_project',
      'worker_unassigned_from_project',
      'manager_assigned_to_project',
      'manager_unassigned_from_project',
    ])
    .optional(),
  role: z.enum(['worker', 'manager']).optional(),
  projectId: z.string().optional(),
});

export class ActivityHistoryFilterParamsDto extends createZodDto(
  activityHistoryFilterParamsSchema,
) {
  @ApiProperty({
    description: 'Sort activity history entries by specified property',
    required: false,
    example: 'date',
  })
  sortBy?: 'date' | 'activityType';

  @ApiProperty({
    description: 'Sort order direction',
    required: false,
    example: 'desc',
  })
  sortOrder?: 'asc' | 'desc';

  @ApiProperty({
    description: 'Filter by activity type',
    required: false,
    example: 'worker_hired',
  })
  activityType?:
    | 'worker_hired'
    | 'worker_quit'
    | 'worker_terminated'
    | 'worker_quit_notice'
    | 'worker_terminated_notice'
    | 'manager_hired'
    | 'manager_quit'
    | 'manager_terminated'
    | 'manager_quit_notice'
    | 'manager_terminated_notice'
    | 'worker_assigned_to_project'
    | 'worker_unassigned_from_project'
    | 'manager_assigned_to_project'
    | 'manager_unassigned_from_project';

  @ApiProperty({
    description: 'Filter by role (worker or manager)',
    required: false,
    example: 'worker',
  })
  role?: 'worker' | 'manager';

  @ApiProperty({
    description: 'Filter by project ID',
    required: false,
    example: 'project-123',
  })
  projectId?: string;
}
