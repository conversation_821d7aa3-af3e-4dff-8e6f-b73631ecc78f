import { Inject, Injectable } from '@nestjs/common';
import { eq, and } from 'drizzle-orm';

import {
  ScheduleNotFoundException,
  NotAuthorizedException,
  PartnerNotFoundException,
} from '@/common/exceptions/custom-exceptions';
import { generateId } from '@/common/utils/generateId';
import { schedules } from '@/modules/db/entities/schedule.entity';
import { scheduleDays } from '@/modules/db/entities/schedule-day.entity';
import { projects } from '@/modules/db/entities/project.entity';
import { ManagersService } from '@/modules/managers/managers.service';

import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { Database } from '../db/db.module';

@Injectable()
export class SchedulesService {
  constructor(
    @Inject('DB') private readonly db: Database,
    private readonly managersService: ManagersService,
  ) {}

  async create(
    createScheduleDto: CreateScheduleDto,
    user: {
      id: string;
      entityId: string;
      role: string;
      permissionType?: string;
    },
  ) {
    let partnerId: string | undefined;

    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (!manager) throw new ScheduleNotFoundException();
      if (manager.permissionType !== 'all') {
        throw new NotAuthorizedException();
      }
      partnerId = manager.partnerId;
    } else {
      throw new NotAuthorizedException();
    }

    if (!partnerId) {
      throw new PartnerNotFoundException();
    }

    return await this.db.transaction(async (tx) => {
      const [schedule] = await tx
        .insert(schedules)
        .values({
          name: createScheduleDto.name,
          description: createScheduleDto.description,
          partnerId,
          requirePhotoValidation: createScheduleDto.requirePhotoValidation,
          reminderTime: createScheduleDto.reminderTime,
        })
        .returning();

      const scheduleDaysData = createScheduleDto.scheduleDays.map((day) => ({
        scheduleId: schedule.id,
        dayOfWeek: day.dayOfWeek,
        isWorkingDay: day.isWorkingDay,
        startTime: day.startTime,
        endTime: day.endTime,
        breaks:
          day.breaks?.map((breakItem) => ({
            ...breakItem,
            id: generateId(),
          })) || [],
      }));

      await tx.insert(scheduleDays).values(scheduleDaysData);

      return await this.findOne(schedule.id, tx);
    });
  }

  async findAll(user: {
    id: string;
    entityId: string;
    role: string;
    permissionType?: string;
  }) {
    let partnerId: string | undefined;
    let projectIds: string[] | undefined;

    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (!manager) throw new ScheduleNotFoundException();
      partnerId = manager.partnerId;

      if (manager.permissionType === 'project_manager') {
        projectIds = manager.projectManagers.map((pm: any) => pm.projectId);
        if (!projectIds.length) return [];
      }
    } else {
      throw new NotAuthorizedException();
    }

    if (!partnerId) {
      throw new PartnerNotFoundException();
    }

    const schedulesQuery = this.db.query.schedules.findMany({
      where: (schedules, { eq, and }) =>
        and(eq(schedules.partnerId, partnerId), eq(schedules.isActive, true)),
      with: {
        scheduleDays: {
          orderBy: (scheduleDays, { asc }) => [asc(scheduleDays.dayOfWeek)],
        },
        project: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    const allSchedules = await schedulesQuery;

    if (projectIds) {
      return allSchedules.filter(
        (schedule) =>
          !schedule.project || projectIds.includes(schedule.project.id),
      );
    }

    return allSchedules;
  }

  async findOne(id: string, tx?: Database) {
    const schedule = await (tx ?? this.db).query.schedules.findFirst({
      where: (schedules, { eq, and }) =>
        and(eq(schedules.id, id), eq(schedules.isActive, true)),
      with: {
        scheduleDays: {
          orderBy: (scheduleDays, { asc }) => [asc(scheduleDays.dayOfWeek)],
        },
        project: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!schedule) {
      throw new ScheduleNotFoundException();
    }

    return schedule;
  }

  async update(id: string, updateScheduleDto: UpdateScheduleDto) {
    const schedule = await this.findOne(id);

    return await this.db.transaction(async (tx) => {
      if (
        updateScheduleDto.name ||
        updateScheduleDto.description ||
        updateScheduleDto.requirePhotoValidation !== undefined ||
        updateScheduleDto.reminderTime
      ) {
        await tx
          .update(schedules)
          .set({
            name: updateScheduleDto.name,
            description: updateScheduleDto.description,
            requirePhotoValidation: updateScheduleDto.requirePhotoValidation,
            reminderTime: updateScheduleDto.reminderTime,
          })
          .where(eq(schedules.id, id));
      }

      if (updateScheduleDto.scheduleDays) {
        await tx.delete(scheduleDays).where(eq(scheduleDays.scheduleId, id));

        const scheduleDaysData = updateScheduleDto.scheduleDays.map((day) => ({
          scheduleId: id,
          dayOfWeek: day.dayOfWeek,
          isWorkingDay: day.isWorkingDay ?? true,
          startTime: day.startTime,
          endTime: day.endTime,
          breaks:
            day.breaks?.map((breakItem) => ({
              ...breakItem,
              id: generateId(),
            })) || [],
        }));

        await tx.insert(scheduleDays).values(scheduleDaysData);
      }

      return await this.findOne(id, tx);
    });
  }

  async remove(id: string) {
    const schedule = await this.findOne(id);

    await this.db
      .update(schedules)
      .set({ isActive: false })
      .where(eq(schedules.id, id));

    await this.db
      .update(projects)
      .set({ scheduleId: null })
      .where(eq(projects.scheduleId, id));
  }

  async assignToProject(scheduleId: string, projectId: string) {
    const schedule = await this.findOne(scheduleId);

    await this.db
      .update(projects)
      .set({ scheduleId })
      .where(eq(projects.id, projectId));
  }

  async removeFromProject(projectId: string) {
    await this.db
      .update(projects)
      .set({ scheduleId: null })
      .where(eq(projects.id, projectId));
  }
}
