import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { createScheduleSchema } from './create-schedule.dto';
import { updateScheduleDaySchema } from './schedule-day.dto';

export const updateScheduleSchema = createScheduleSchema.partial().extend({
  scheduleDays: z.array(updateScheduleDaySchema).optional(),
});

export class UpdateScheduleDto extends createZodDto(updateScheduleSchema) {}
