import { ApiProperty } from '@nestjs/swagger';
import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { schedules } from '@/modules/db/entities/schedule.entity';

import { ScheduleBreakDto } from './schedule-break.dto';

export const scheduleSchema = createSelectSchema(schedules);

export const scheduleWithDetailsSchema = scheduleSchema.extend({
  breaks: z.array(z.object({
    id: z.string(),
    name: z.string(),
    startTime: z.string(),
    endTime: z.string(),
  })),
});

export class ScheduleDto extends createZodDto(scheduleSchema) {}

export class ScheduleWithDetailsDto extends createZodDto(scheduleWithDetailsSchema) {
  @ApiProperty({ type: [ScheduleBreakDto] })
  breaks!: ScheduleBreakDto[];
}

export class ScheduleWithProjectDto extends ScheduleWithDetailsDto {
  @ApiProperty({ required: false })
  project?: {
    id: string;
    name: string;
  };
}
