import { ApiProperty } from '@nestjs/swagger';
import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { schedules } from '@/modules/db/entities/schedule.entity';

import { ScheduleDayDto } from './schedule-day.dto';

export const scheduleSchema = createSelectSchema(schedules);

export const scheduleWithDetailsSchema = scheduleSchema.extend({
  scheduleDays: z.array(
    z.object({
      id: z.string(),
      dayOfWeek: z.string(),
      isWorkingDay: z.boolean(),
      startTime: z.string().nullable(),
      endTime: z.string().nullable(),
      breaks: z
        .array(
          z.object({
            id: z.string(),
            name: z.string().optional().nullable(),
            startTime: z.string(),
            endTime: z.string(),
          }),
        )
        .nullable(),
    }),
  ),
});

export const scheduleWithProjectSchema = scheduleWithDetailsSchema.extend({
  project: z.object({
    id: z.string(),
    name: z.string(),
  }),
});
export class ScheduleDto extends createZodDto(scheduleSchema) {}

export class ScheduleWithDetailsDto extends createZodDto(
  scheduleWithDetailsSchema,
) {}

export class ScheduleWithProjectDto extends createZodDto(
  scheduleWithProjectSchema,
) {}
