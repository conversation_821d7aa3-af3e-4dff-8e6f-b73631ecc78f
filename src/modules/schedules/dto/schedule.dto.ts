import { ApiProperty } from '@nestjs/swagger';
import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { schedules } from '@/modules/db/entities/schedule.entity';

import { ScheduleDayDto } from './schedule-day.dto';

export const scheduleSchema = createSelectSchema(schedules);

export const scheduleWithDetailsSchema = scheduleSchema.extend({
  days: z.array(
    z.object({
      id: z.string(),
      dayOfWeek: z.string(),
      isWorkingDay: z.boolean(),
      startTime: z.string().nullable(),
      endTime: z.string().nullable(),
      breaks: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          startTime: z.string(),
          endTime: z.string(),
        }),
      ),
      workLocationId: z.string().nullable(),
    }),
  ),
});

export class ScheduleDto extends createZodDto(scheduleSchema) {}

export class ScheduleWithDetailsDto extends createZodDto(
  scheduleWithDetailsSchema,
) {
  @ApiProperty({ type: [ScheduleDayDto] })
  days!: ScheduleDayDto[];
}

export class ScheduleWithProjectDto extends ScheduleWithDetailsDto {
  @ApiProperty({ required: false })
  project?: {
    id: string;
    name: string;
  };
}
