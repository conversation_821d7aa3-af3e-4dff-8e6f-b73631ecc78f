import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import {
  createScheduleDayBreakSchema,
  scheduleDayBreakSchema,
} from './schedule-break.dto';

export const dayOfWeekSchema = z.enum([
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
]);

export const scheduleDaySchema = z.object({
  id: z.string().min(1),
  dayOfWeek: dayOfWeekSchema,
  isWorkingDay: z.boolean().default(true),
  startTime: z
    .string()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: 'Start time must be in HH:MM format',
    })
    .optional(),
  endTime: z
    .string()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: 'End time must be in HH:MM format',
    })
    .optional(),
  breaks: z.array(scheduleDayBreakSchema).default([]),
});

export const createScheduleDaySchema = z.object({
  dayOfWeek: dayOfWeekSchema,
  isWorkingDay: z.boolean().default(true),
  startTime: z
    .string()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: 'Start time must be in HH:MM format',
    })
    .optional(),
  endTime: z
    .string()
    .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: 'End time must be in HH:MM format',
    })
    .optional(),
  breaks: z.array(createScheduleDayBreakSchema).default([]),
});

export const updateScheduleDaySchema = createScheduleDaySchema
  .partial()
  .extend({
    dayOfWeek: dayOfWeekSchema, // dayOfWeek should always be required for identification
  });

export class ScheduleDayDto extends createZodDto(scheduleDaySchema) {}
export class CreateScheduleDayDto extends createZodDto(
  createScheduleDaySchema,
) {}
export class UpdateScheduleDayDto extends createZodDto(
  updateScheduleDaySchema,
) {}

export type DayOfWeek = z.infer<typeof dayOfWeekSchema>;
