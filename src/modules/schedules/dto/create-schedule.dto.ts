import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { createScheduleDaySchema } from './schedule-day.dto';

export const reminderTimeSchema = z.enum([
  'schedule_start',
  'schedule_end',
  'schedule_start_and_end',
  'all_breaks_and_schedule',
  'none',
]);

export const createScheduleSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  photoPresenceValidation: z.boolean().default(true),
  reminderTime: reminderTimeSchema.default('schedule_start_and_end'),
  days: z
    .array(createScheduleDaySchema)
    .min(1, 'At least one day must be specified'),
});

export class CreateScheduleDto extends createZodDto(createScheduleSchema) {}

export type ReminderTime = z.infer<typeof reminderTimeSchema>;
