import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const scheduleDayBreakSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1).max(100).optional(),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Start time must be in HH:MM format',
  }),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'End time must be in HH:MM format',
  }),
});

export const createScheduleDayBreakSchema = scheduleDayBreakSchema.omit({
  id: true,
});

export class ScheduleDayBreakDto extends createZodDto(scheduleDayBreakSchema) {}
export class CreateScheduleDayBreakDto extends createZodDto(
  createScheduleDayBreakSchema,
) {}
