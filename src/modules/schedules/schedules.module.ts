import { Module, forwardRef } from '@nestjs/common';

import { ManagersModule } from '../managers/managers.module';
import { SchedulesController } from './schedules.controller';
import { SchedulesService } from './schedules.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => ManagersModule), forwardRef(() => UsersModule)],
  controllers: [SchedulesController],
  providers: [SchedulesService],
  exports: [SchedulesService],
})
export class SchedulesModule {}
