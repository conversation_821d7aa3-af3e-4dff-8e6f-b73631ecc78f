import { Module, forwardRef } from '@nestjs/common';

import { ManagersModule } from '../managers/managers.module';
import { SchedulesController } from './schedules.controller';
import { SchedulesService } from './schedules.service';

@Module({
  imports: [forwardRef(() => ManagersModule)],
  controllers: [SchedulesController],
  providers: [SchedulesService],
  exports: [SchedulesService],
})
export class SchedulesModule {}
