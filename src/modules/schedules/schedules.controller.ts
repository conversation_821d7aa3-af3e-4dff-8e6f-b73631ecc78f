import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  NotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
} from '@/common/dto/error-response.dto';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import {
  ScheduleWithDetailsDto,
  ScheduleWithProjectDto,
} from './dto/schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { SchedulesService } from './schedules.service';

@ApiTags('Schedules')
@ApiBearerAuth()
@Controller('schedules')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.Partner, { type: Role.Manager })
export class SchedulesController {
  constructor(private readonly schedulesService: SchedulesService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new schedule',
    description: 'Create a new schedule with breaks and settings',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Schedule created successfully',
    type: ScheduleWithDetailsDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
    type: BadRequestErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to create schedules',
    type: ForbiddenErrorResponseDto,
  })
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  create(
    @User() user: RequestUserType,
    @Body() createScheduleDto: CreateScheduleDto,
  ) {
    return this.schedulesService.create(createScheduleDto, user);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all schedules',
    description:
      'Retrieve all schedules for the authenticated partner or manager',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of schedules retrieved successfully',
    type: [ScheduleWithProjectDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access schedules',
    type: ForbiddenErrorResponseDto,
  })
  findAll(@User() user: RequestUserType): Promise<ScheduleWithProjectDto[]> {
    return this.schedulesService.findAll(user);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get schedule by ID',
    description: 'Retrieve a specific schedule with all details',
  })
  @ApiParam({
    name: 'id',
    description: 'Schedule unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Schedule found successfully',
    type: ScheduleWithProjectDto,
  })
  @ApiNotFoundResponse({
    description: 'Schedule not found',
    type: NotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this schedule',
    type: ForbiddenErrorResponseDto,
  })
  findOne(@Param('id') id: string): Promise<ScheduleWithProjectDto> {
    return this.schedulesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update schedule',
    description: 'Update an existing schedule',
  })
  @ApiParam({
    name: 'id',
    description: 'Schedule unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Schedule updated successfully',
    type: ScheduleWithDetailsDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
    type: BadRequestErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Schedule not found',
    type: NotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update this schedule',
    type: ForbiddenErrorResponseDto,
  })
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  update(
    @Param('id') id: string,
    @Body() updateScheduleDto: UpdateScheduleDto,
  ) {
    return this.schedulesService.update(id, updateScheduleDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete schedule',
    description:
      'Soft delete a schedule and remove it from any assigned projects',
  })
  @ApiParam({
    name: 'id',
    description: 'Schedule unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Schedule deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'Schedule not found',
    type: NotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to delete this schedule',
    type: ForbiddenErrorResponseDto,
  })
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  remove(@Param('id') id: string) {
    return this.schedulesService.remove(id);
  }
}
