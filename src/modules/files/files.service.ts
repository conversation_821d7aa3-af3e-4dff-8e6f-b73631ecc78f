import * as path from 'path';
import { Readable } from 'stream';

import { DeleteObjectCommand, GetObjectCommand, S3 } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { add } from 'date-fns';
import { eq } from 'drizzle-orm';
import { Response } from 'express';

import { Forwarded } from '@/common/types';
import { generateId } from '@/common/utils/generateId';

import { Database } from '../db/db.module';
import { files } from '../db/entities/file.entity';
import { FilePermissionsService } from '../file-permissions/file-permissions.service';
import { ManagersService } from '../managers/managers.service';
import { PartnersService } from '../partners/partners.service';
import { WorkersService } from '../workers/workers.service';
import { CreateFileDto } from './dto/create-file.dto';
import { UploadFileParamsDto } from './dto/upload-file-params.dto';
import {
  FileNotFoundException,
  InsufficientFilePermissionsException,
  PartnerNotFoundException,
} from '@/common/exceptions/custom-exceptions';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { Agent } from 'http';

const agent = new Agent({
  keepAlive: true,
  keepAliveMsecs: 1000,
  maxSockets: 200,
  maxFreeSockets: 200,
  timeout: 60000, // Increased from 30000 to 60000ms
});

@Injectable()
export class FilesService {
  private readonly s3: S3;
  private readonly AWS_S3_BUCKET: string;

  constructor(
    @Inject('DB') private readonly db: Database,
    private readonly configService: ConfigService,
    private readonly filePermissionsService: FilePermissionsService,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
    @Inject(forwardRef(() => PartnersService))
    private readonly partnersService: Forwarded<PartnersService>,
  ) {
    if (!this.configService.get<string>('s3.bucket'))
      throw new Error('S3 bucket not configured');
    if (!this.configService.get<string>('s3.accessKeyId'))
      throw new Error('S3 access key ID not configured');
    if (!this.configService.get<string>('s3.secretAccessKey'))
      throw new Error('S3 secret access key not configured');

    this.AWS_S3_BUCKET = this.configService.get<string>('s3.bucket')!;
    this.s3 = new S3({
      credentials: {
        accessKeyId: this.configService.get<string>('s3.accessKeyId')!,
        secretAccessKey: this.configService.get<string>('s3.secretAccessKey')!,
      },
      region: 'eu-north-1',
      requestHandler: new NodeHttpHandler({
        connectionTimeout: 10000,
        socketTimeout: 60000,
        httpAgent: agent,
      }),
      maxAttempts: 3,
    });
  }

  async create(createFileDto: CreateFileDto) {
    return (
      await this.db
        .insert(files)
        .values(createFileDto)
        .returning({ id: files.id })
    )[0];
  }

  findOne(id: string) {
    return this.db.query.files.findFirst({
      where: (files, { eq }) => eq(files.id, id),
      with: {
        permissions: true,
      },
    });
  }

  delete(id: string) {
    return this.db
      .update(files)
      .set({ deletedAt: new Date() })
      .where(eq(files.id, id));
  }

  async uploadFile(
    // @ts-expect-error ts server
    file: Express.Multer.File,
    params: UploadFileParamsDto,
    userId: string,
  ) {
    const safeKey = this.generateSafeKey(file.originalname);
    const security = params.security ?? 'private';
    try {
      const s3Response = await new Upload({
        client: this.s3,
        params: {
          Bucket: this.AWS_S3_BUCKET,
          Key: safeKey,
          Body: file.buffer,
          ContentType: file.mimetype,
          ContentDisposition: 'inline',
          ACL: security === 'public' ? 'public-read' : 'private',
        },
        queueSize: 4, // Number of parts to upload concurrently
        partSize: 1024 * 1024 * 5, // 5MB part size
        leavePartsOnError: false, // Clean up failed uploads
      }).done();
      const fileId = await this.create({
        s3Key: safeKey,
        fileName: file.originalname,
        fileType: file.mimetype,
        publicFileUrl: security === 'public' ? s3Response.Location : null,
        scope: params.scope,
        security,
      });

      switch (params.scope) {
        case 'user':
          await this.filePermissionsService.create({
            fileId: fileId.id,
            userId: userId,
            scope: 'user',
          });
          break;
        case 'partner':
          const worker = await this.workersService.findOneByUserId(userId);
          const partner = await this.partnersService.findOneByUserId(userId);
          const manager = await this.managersService.findOneByUserId(userId);

          let partnerUserId: string | undefined;

          if (worker?.partner?.user?.id) {
            partnerUserId = worker.partner.user.id;
          } else if (partner) {
            partnerUserId = userId;
          } else if (manager) {
            const managerPartner = manager.partner?.userId
              ? manager.partner
              : manager.partnerId
                ? await this.partnersService.findOne(manager.partnerId)
                : null;
            if (!managerPartner) {
              throw new PartnerNotFoundException();
            }
            partnerUserId = managerPartner.userId;
          } else {
            throw new PartnerNotFoundException();
          }

          await this.filePermissionsService.create(
            {
              fileId: fileId.id,
              userId: partnerUserId,
              scope: 'partner',
            },
            {
              fileId: fileId.id,
              userId,
              scope: 'user',
            },
          );
          break;
        default:
          break;
      }
      return fileId;
    } catch (e) {
      console.error('Upload error:', e);

      // Provide more specific error messages based on the error type
      if (e && typeof e === 'object' && 'name' in e) {
        if (e.name === 'TimeoutError') {
          throw new InternalServerErrorException(
            'Upload timeout - please try again',
          );
        } else if (e.name === 'NetworkError') {
          throw new InternalServerErrorException(
            'Network error during upload - please check your connection',
          );
        }
      }

      // Check for AWS SDK specific error metadata
      if (
        e &&
        typeof e === 'object' &&
        '$metadata' in e &&
        e.$metadata &&
        typeof e.$metadata === 'object' &&
        'httpStatusCode' in e.$metadata
      ) {
        const statusCode = e.$metadata.httpStatusCode;
        if (statusCode === 403) {
          throw new InternalServerErrorException('Access denied to S3 bucket');
        } else if (statusCode === 404) {
          throw new InternalServerErrorException('S3 bucket not found');
        }
      }

      throw new InternalServerErrorException('Failed to upload file');
    }
  }

  async checkFileExists(id: string) {
    const file = await this.findOne(id);
    if (!file) return null;
    try {
      await this.s3.send(
        new GetObjectCommand({
          Bucket: this.AWS_S3_BUCKET,
          Key: file.s3Key,
        }),
      );
      return file;
    } catch (e) {
      console.error('Check error:', e);
      return null;
    }
  }

  async deleteFile(id: string) {
    const file = await this.checkFileExists(id);
    if (!file) throw new FileNotFoundException();

    try {
      await this.s3.send(
        new DeleteObjectCommand({
          Bucket: this.AWS_S3_BUCKET,
          Key: file.s3Key,
        }),
      );
    } catch (e) {
      console.error('Delete error:', e);
      throw new Error('Failed to delete file');
    }
  }

  async streamFile(id: string, res: Response) {
    const file = await this.checkFileExists(id);
    if (!file) throw new FileNotFoundException();

    try {
      const command = new GetObjectCommand({
        Bucket: this.AWS_S3_BUCKET,
        Key: file.s3Key,
      });

      const s3Object = await this.s3.send(command);

      res.setHeader(
        'Content-Type',
        s3Object.ContentType || 'application/octet-stream',
      );
      res.setHeader('Content-Disposition', 'inline');

      const body = s3Object.Body;
      if (body instanceof Readable) {
        body.on('error', (err) => {
          body.destroy(); // Explicitly destroy the stream
          res.status(500).end('Stream error');
        });
        body.pipe(res);
      } else {
        throw new Error('Unexpected response body type');
      }
    } catch (e) {
      console.error(e);
      res.status(404).send('File not found');
    }
  }

  async generatePresignedUrl(id: string) {
    const file = await this.checkFileExists(id);
    if (!file) throw new FileNotFoundException();
    try {
      const command = new GetObjectCommand({
        Bucket: this.AWS_S3_BUCKET,
        Key: file.s3Key,
      });

      const signedUrl = await getSignedUrl(this.s3, command, {
        expiresIn: 60 * 60 * 1,
      });
      return {
        url: signedUrl,
        expiresAt: add(new Date(), {
          seconds: 60 * 60 * 1,
        }),
      };
    } catch (e) {
      console.error(e);
      throw new Error('Failed to generate pre-signed URL');
    }
  }

  async getFileUrl(fileId: string, userId: string) {
    const file = await this.checkFileExists(fileId);
    if (!file) throw new FileNotFoundException();

    if (file.security === 'public' && file.publicFileUrl) {
      return {
        url: file.publicFileUrl,
        expiresAt: null,
      };
    }

    const hasDirectPermission = file.permissions
      .filter((p) => p.scope === file.scope || p.scope === 'user')
      .find((p) => p.userId === userId);

    if (hasDirectPermission) {
      return this.generatePresignedUrl(fileId);
    }

    if (file.scope === 'partner') {
      const partnerPermission = file.permissions.find(
        (p) => p.scope === 'partner',
      );
      if (!partnerPermission) {
        throw new InsufficientFilePermissionsException();
      }
      const partnerUserId = partnerPermission.userId;

      const worker = await this.workersService.findOneByUserId(userId);
      const manager = await this.managersService.findOneByUserId(userId);
      const partner = await this.partnersService.findOneByUserId(userId);

      const hasAccess =
        (worker && worker.partner.user.id === partnerUserId) ||
        (manager && manager.partner.userId === partnerUserId) ||
        (partner && partner.userId === partnerUserId);

      if (hasAccess) {
        return this.generatePresignedUrl(fileId);
      }
    }

    throw new InsufficientFilePermissionsException();
  }

  private generateSafeKey(originalName: string): string {
    const cleanFileName = originalName
      .replace(/[^a-zA-Z0-9_\-.]+/g, '')
      .replace(/\/+/g, '-')
      .replace(/^\.+/, '');

    const extension = path.extname(cleanFileName);
    const baseName = path.basename(cleanFileName, extension);
    const uniqueId = generateId();

    return `${uniqueId}-${baseName}${extension}`.substring(0, 255);
  }
}
