import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const registrationCodeSchema = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string(),
  status: z.enum(['active', 'inactive']),
});

const codeParamSchema = z.object({
  code: z
    .string()
    .regex(
      /^[a-zA-Z0-9]+$/,
      'Invalid code format: only alphanumeric characters are allowed.',
    ),
});

export class RegistrationCodeDto extends createZodDto(registrationCodeSchema) {}

export class CodeParamDto extends createZodDto(codeParamSchema) {}
