import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  UseGuards,
  PipeTransform,
  Injectable,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  RegistrationCodeNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RegistrationCodeNotFoundException } from '@/common/exceptions/custom-exceptions';
import { RolesGuard } from '@/common/guards/roles.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { z } from 'zod';
import { InvalidRegistrationCodeException } from '@/common/exceptions/custom-exceptions';

import { CodeDetailsDto } from './dto/code-details.dto';
import { CodeParamDto, RegistrationCodeDto } from './dto/code.dto';
import { CreateCodeResponseDto } from './dto/create-code-response.dto';
import { CreateCodeDto } from './dto/create-code.dto';
import { UpdateCodeDto } from './dto/update-code.dto';
import { RegistrationCodesService } from './registration-codes.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Registration Codes')
@ApiBearerAuth()
@Controller('registration-codes')
export class RegistrationCodesController {
  constructor(
    private readonly registrationCodesService: RegistrationCodesService,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'List registration codes',
    description: 'Retrieve all registration codes for the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Registration codes retrieved successfully',
    type: [RegistrationCodeDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to view codes',
    type: ForbiddenErrorResponseDto,
  })
  getAll(@User() user: RequestUserType): Promise<RegistrationCodeDto[]> {
    return this.registrationCodesService.getAllForUser(user);
  }

  @Get('/:code')
  @ApiOperation({
    summary: 'Get code details',
    description: 'Retrieve details about a specific registration code',
  })
  @ApiParam({
    name: 'code',
    description: 'Registration code to check',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Code details retrieved successfully',
    type: CodeDetailsDto,
  })
  @ApiNotFoundResponse({
    description: 'Code not found or expired',
    type: RegistrationCodeNotFoundErrorResponseDto,
  })
  async getDetails(
    @Param() codeParamDto: CodeParamDto,
  ): Promise<CodeDetailsDto> {
    const codeDetails = await this.registrationCodesService.getDetails(
      codeParamDto.code,
    );
    if (!codeDetails) throw new RegistrationCodeNotFoundException();
    return codeDetails;
  }

  @Patch('/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({
    summary: 'Update registration code',
    description: 'Update registration code by id',
  })
  @ApiParam({
    name: 'id',
    description: 'Currently active registration code id',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully updated registration code',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update codes',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Registration code not found',
    type: RegistrationCodeNotFoundErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid update data',
    type: ValidationErrorResponseDto,
  })
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateCodeDto: UpdateCodeDto,
    @User() user: RequestUserType,
  ) {
    return this.registrationCodesService.update(id, updateCodeDto, user);
  }

  @Delete('/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({
    summary: 'Deactivate registration code',
    description: 'Deactivate registration code by id',
  })
  @ApiParam({
    name: 'id',
    description: 'Currently active registration code id',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully deactivated registration code',
  })
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  delete(@Param('id') id: string, @User() user: RequestUserType) {
    return this.registrationCodesService.deactivate(id, user);
  }

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'Generate registration code',
    description:
      'Create a new registration code for worker or manager registration',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Registration code generated successfully',
    type: CreateCodeResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid code generation parameters' })
  @ApiForbiddenResponse({ description: 'Not authorized to generate codes' })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  async create(
    @User() user: RequestUserType,
    @Body() createCodeDto: CreateCodeDto,
  ): Promise<CreateCodeResponseDto> {
    return this.registrationCodesService.create(createCodeDto, user);
  }
}
