import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { isBefore } from 'date-fns';
import { and, eq, lt } from 'drizzle-orm';

import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';
import { generatePassword } from '@/common/utils/generatePassword';

import { RequestUserDto, RequestUserType } from '../auth/dto/request-user.dto';
import { Database } from '../db/db.module';
import { registrationCodes } from '../db/entities/registration-code.entity';
import { UsersService } from '../users/users.service';
import { CreateCodeDto } from './dto/create-code.dto';
import { UpdateCodeDto } from './dto/update-code.dto';
import { ManagersService } from '../managers/managers.service';
import {
  RegistrationCodeNotFoundException,
  ManagerPermissionRestrictionException,
  InvalidUserContextException,
  CodeNameAlreadyExistsException,
} from '@/common/exceptions/custom-exceptions';
@Injectable()
export class RegistrationCodesService {
  private readonly logger = new Logger(RegistrationCodesService.name);

  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
  ) {}

  async getAllForUser(user: RequestUserType) {
    if (user.role !== 'manager' && user.role !== 'partner') {
      return [];
    }
    return this.db.query.registrationCodes.findMany({
      where: (registrationCodes, { eq, or }) =>
        or(
          user.role === 'manager'
            ? eq(registrationCodes.managerId, user.entityId)
            : undefined,
          user.role === 'partner'
            ? eq(registrationCodes.partnerId, user.entityId)
            : undefined,
        ),
      columns: {
        id: true,
        name: true,
        code: true,
        status: true,
      },
    });
  }

  private async generateCode(): Promise<string> {
    const code = generatePassword(10, {
      digits: true,
      lowerCaseAlphabets: false,
      upperCaseAlphabets: true,
      specialChars: false,
    });
    const codeExists = await this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq, and }) =>
        and(eq(registrationCodes.code, code)),
      columns: {
        id: true,
      },
      with: {
        requests: {
          where: (registrationRequests, { eq }) =>
            eq(registrationRequests.status, 'approved'),
        },
      },
    });
    if (codeExists) return this.generateCode();
    return code;
  }

  async create(createCodeDto: CreateCodeDto, user: RequestUserDto) {
    const code = await this.generateCode();
    if (
      user.role === 'manager' &&
      createCodeDto.role === 'manager' &&
      createCodeDto.managerPermissionType === ManagerPermissionType.All
    )
      throw new ManagerPermissionRestrictionException();

    let partnerId = user.role === 'partner' ? user.entityId : null;
    const managerId = user.role === 'manager' ? user.entityId : null;
    if (!partnerId && !managerId) throw new InvalidUserContextException();

    if (!partnerId && managerId) {
      partnerId = (await this.managersService.getPartnerId(managerId)) ?? null;
    }

    if (!partnerId) throw new InvalidUserContextException();

    const codeExists = await this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq, and }) =>
        and(
          eq(registrationCodes.partnerId, partnerId),
          eq(registrationCodes.name, createCodeDto.name),
        ),
    });

    if (codeExists)
      throw new CodeNameAlreadyExistsException();

    await this.db.insert(registrationCodes).values({
      ...createCodeDto,
      partnerId,
      managerId,
      code,
      name: createCodeDto.name,
      role: createCodeDto.role,
    });

    return { code };
  }

  async findByCode(code: string) {
    return this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq }) => eq(registrationCodes.code, code),
    });
  }

  async findOne(id: string) {
    return this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq }) => eq(registrationCodes.id, id),
      with: {
        requests: true,
        partner: true,
      },
    });
  }

  async check({ code, role }: { code: string; role: 'worker' | 'manager' }) {
    const registrationInvite = await this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq, and }) =>
        and(
          eq(registrationCodes.code, code),
          eq(registrationCodes.status, 'active'),
          eq(registrationCodes.role, role),
        ),
      with: {
        requests: true,
      },
    });

    const isInviteExpired =
      registrationInvite?.expiresAt &&
      isBefore(new Date(registrationInvite.expiresAt), new Date());

    if (
      !registrationInvite ||
      (registrationInvite.oneTimeCode &&
        registrationInvite.requests.length >= 1) ||
      isInviteExpired
    ) {
      if (registrationInvite?.status === 'active')
        await this.deactivate(registrationInvite.id);
      return { valid: false as const };
    }
    return { valid: true as const, data: registrationInvite };
  }

  async getDetails(code: string) {
    const registrationInvite = await this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq, and }) =>
        and(
          eq(registrationCodes.code, code),
          eq(registrationCodes.status, 'active'),
        ),
      with: {
        partner: {
          columns: {
            workLocation: true,
            registrationAddress: true,
          },
        },
      },
    });

    if (!registrationInvite) return { valid: false as const };

    return {
      valid: true as const,
      data: {
        role: registrationInvite.role,
      },
    };
  }

  async findOneWithWorkers(id: string) {
    const registrationCodes = await this.db.query.registrationCodes.findFirst({
      where: (registrationCodes, { eq }) => eq(registrationCodes.id, id),
      with: {
        requests: {
          with: {
            worker: true,
          },
        },
      },
    });
    if (!registrationCodes) return null;

    const { requests, ...rest } = registrationCodes;

    return {
      ...rest,
      workers: requests
        .filter((request) => request.worker)
        .map((request) => request.worker),
      requests,
    };
  }

  async update(
    id: string,
    updateCodeDto: UpdateCodeDto,
    user: RequestUserType,
  ) {
    return this.db
      .update(registrationCodes)
      .set(updateCodeDto)
      .where(
        and(
          eq(registrationCodes.id, id),
          user.role === 'partner'
            ? eq(registrationCodes.partnerId, user.entityId)
            : eq(registrationCodes.managerId, user.entityId),
        ),
      );
  }

  async deactivate(id: string, user?: RequestUserType) {
    const codeWithWorkers = await this.findOneWithWorkers(id);
    if (!codeWithWorkers)
      throw new RegistrationCodeNotFoundException();

    // if (
    //   codeWithWorkers &&
    //   codeWithWorkers.workers.length &&
    //   codeWithWorkers.requests.length
    // ) {
    //   await this.workersService.changeApprovalState(
    //     codeWithWorkers.workers
    //       .filter((w) => w?.approvalState === 'pending')
    //       .map((w) => w!.id),
    //     'rejected',
    //   );

    //   await this.registrationRequestsService.rejectAllPendingForCode(id);
    // }

    return this.db
      .update(registrationCodes)
      .set({
        status: 'inactive',
      })
      .where(
        and(
          eq(registrationCodes.id, id),
          user
            ? user.role === 'partner'
              ? eq(registrationCodes.partnerId, user.entityId)
              : eq(registrationCodes.managerId, user.entityId)
            : undefined,
        ),
      );
  }

  @Cron(CronExpression.EVERY_12_HOURS)
  async cleanupExpiredRegistrationCodes() {
    try {
      const result = await this.db
        .update(registrationCodes)
        .set({ status: 'inactive' })
        .where(
          and(
            eq(registrationCodes.status, 'active'),
            lt(registrationCodes.expiresAt, new Date()),
          ),
        );

      this.logger.log(
        `Marked ${result.rowCount} expired registration codes as expired`,
      );
    } catch (error) {
      this.logger.error('Failed to clean up expired registration codes', error);
    }
  }
}
