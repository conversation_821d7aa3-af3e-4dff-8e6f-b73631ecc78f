import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { PartnersService } from '../partners/partners.service';
import { SubscriptionService } from './subscription.service';

@ApiTags('Subscription')
@ApiBearerAuth()
@Controller('subscription')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly partnersService: PartnersService,
  ) {}

  @Get('info')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Get subscription information',
    description: 'Retrieve current subscription tier, limits, and usage for the authenticated partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription information retrieved successfully',
  })
  async getSubscriptionInfo(@User() user: RequestUserType) {
    const partner = await this.partnersService.findOneByUserId(user.id);
    if (!partner) {
      throw new Error('Partner not found');
    }

    return this.subscriptionService.getSubscriptionInfo(partner.id);
  }
}
