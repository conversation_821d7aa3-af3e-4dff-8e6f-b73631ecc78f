import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionService } from './subscription.service';
import { SubscriptionTier, SubscriptionFeature, SubscriptionResource } from '@/common/enums/subscription.enum';

describe('SubscriptionService', () => {
  let service: SubscriptionService;
  let mockDb: any;

  beforeEach(async () => {
    mockDb = {
      query: {
        partners: {
          findFirst: jest.fn(),
        },
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
      ],
    }).compile();

    service = module.get<SubscriptionService>(SubscriptionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPartnerSubscriptionTier', () => {
    it('should return free tier by default', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue(null);
      
      const tier = await service.getPartnerSubscriptionTier('partner-id');
      
      expect(tier).toBe(SubscriptionTier.Free);
    });

    it('should return partner subscription tier', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue({
        subscriptionTier: 'enterprise',
      });
      
      const tier = await service.getPartnerSubscriptionTier('partner-id');
      
      expect(tier).toBe(SubscriptionTier.Enterprise);
    });
  });

  describe('isFeatureAvailable', () => {
    it('should return false for photo confirmation on free tier', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue({
        subscriptionTier: 'free',
      });
      
      const isAvailable = await service.isFeatureAvailable(
        'partner-id',
        SubscriptionFeature.PhotoConfirmation
      );
      
      expect(isAvailable).toBe(false);
    });

    it('should return true for photo confirmation on small business tier', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue({
        subscriptionTier: 'small_business',
      });
      
      const isAvailable = await service.isFeatureAvailable(
        'partner-id',
        SubscriptionFeature.PhotoConfirmation
      );
      
      expect(isAvailable).toBe(true);
    });
  });

  describe('canAddResource', () => {
    it('should return false when worker limit exceeded on free tier', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue({
        subscriptionTier: 'free',
      });
      
      const canAdd = await service.canAddResource(
        'partner-id',
        SubscriptionResource.Workers,
        3 // Free tier limit is 3
      );
      
      expect(canAdd).toBe(false);
    });

    it('should return true when under worker limit on free tier', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue({
        subscriptionTier: 'free',
      });
      
      const canAdd = await service.canAddResource(
        'partner-id',
        SubscriptionResource.Workers,
        2 // Under the limit of 3
      );
      
      expect(canAdd).toBe(true);
    });

    it('should return true for unlimited resources on enterprise tier', async () => {
      mockDb.query.partners.findFirst.mockResolvedValue({
        subscriptionTier: 'enterprise',
      });
      
      const canAdd = await service.canAddResource(
        'partner-id',
        SubscriptionResource.Workers,
        1000 // Any number should work for unlimited
      );
      
      expect(canAdd).toBe(true);
    });
  });
});
