import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../../auth/guards/jwt.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { SubscriptionGuard } from '@/common/guards/subscription.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  RequirePhotoConfirmation,
  CheckWorkersLimit,
  CheckProjectsLimit,
  CheckManagersLimit,
} from '@/common/decorators/subscription.decorator';
import { Role } from '@/common/enums';
import { RequestUserType } from '../../auth/dto/request-user.dto';

/**
 * Example controller demonstrating subscription-based feature and resource limits
 * This controller shows how to use various subscription decorators
 */
@ApiTags('Subscription Examples')
@ApiBearerAuth()
@Controller('subscription-examples')
@UseGuards(JwtAuthGuard, RolesGuard, SubscriptionGuard)
export class SubscriptionExamplesController {
  // Feature-based restrictions

  // @Get('web-dashboard')
  // @RequireWebVersion()
  // @Roles(Role.Partner, Role.Manager)
  // @ApiOperation({
  //   summary: 'Access web dashboard',
  //   description: 'Requires web version feature (Small Business tier or higher)',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Web dashboard data',
  // })
  // getWebDashboard(@User() user: RequestUserType) {
  //   return {
  //     message: 'Welcome to the web dashboard!',
  //     features: ['analytics', 'reports', 'management'],
  //     user: user.id,
  //   };
  // }

  @Post('photo-validation')
  @RequirePhotoConfirmation()
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Create photo validation',
    description:
      'Requires photo confirmation feature (Small Business tier or higher)',
  })
  createPhotoValidation(@User() user: RequestUserType) {
    return {
      message: 'Photo validation created successfully',
      validationId: 'validation-123',
      requiredBy: user.id,
    };
  }

  // Resource limit restrictions

  @Post('test-worker-limit')
  @CheckWorkersLimit()
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Test worker creation limit',
    description: 'Checks if partner can add more workers based on subscription',
  })
  testWorkerLimit(@User() user: RequestUserType) {
    return {
      message: 'Worker limit check passed - you can add more workers',
      partnerId: user.entityId,
    };
  }

  @Post('test-project-limit')
  @CheckProjectsLimit()
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Test project creation limit',
    description:
      'Checks if partner can add more projects based on subscription',
  })
  testProjectLimit(@User() user: RequestUserType) {
    return {
      message: 'Project limit check passed - you can add more projects',
      partnerId: user.entityId,
    };
  }

  @Post('test-manager-limit')
  @CheckManagersLimit()
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Test manager creation limit',
    description:
      'Checks if partner can add more managers based on subscription',
  })
  testManagerLimit(@User() user: RequestUserType) {
    return {
      message: 'Manager limit check passed - you can add more managers',
      partnerId: user.entityId,
    };
  }
}
