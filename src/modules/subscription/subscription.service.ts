import { Inject, Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';

import {
  SubscriptionTier,
  SubscriptionLimits,
  SUBSCRIPTION_LIMITS,
  SubscriptionFeature,
  SubscriptionResource,
} from '@/common/enums/subscription.enum';
import {
  SubscriptionLimitExceededException,
  FeatureNotAvailableException,
  WorkersLimitExceededException,
  ProjectsLimitExceededException,
  ManagersLimitExceededException,
  WebVersionNotAvailableException,
  PhotoConfirmationNotAvailableException,
  FinancialStatisticsNotAvailableException,
  ChartsNotAvailableException,
  AdvancedAnalyticsNotAvailableException,
  ReportGenerationNotAvailableException,
} from '@/common/exceptions/subscription-exceptions';

import { Database } from '../db/db.module';
import { partners } from '../db/entities/partner.entity';

@Injectable()
export class SubscriptionService {
  constructor(@Inject('DB') private readonly db: Database) {}

  async getPartnerSubscriptionTier(
    partnerId: string,
  ): Promise<SubscriptionTier> {
    const partner = await this.db.query.partners.findFirst({
      where: eq(partners.id, partnerId),
      columns: {
        subscriptionTier: true,
      },
    });

    return (
      (partner?.subscriptionTier as SubscriptionTier) || SubscriptionTier.Free
    );
  }

  async getPartnerSubscriptionLimits(
    partnerId: string,
  ): Promise<SubscriptionLimits> {
    const tier = await this.getPartnerSubscriptionTier(partnerId);
    return SUBSCRIPTION_LIMITS[tier];
  }

  async isFeatureAvailable(
    partnerId: string,
    feature: SubscriptionFeature,
  ): Promise<boolean> {
    const limits = await this.getPartnerSubscriptionLimits(partnerId);
    return limits[feature];
  }

  async canAddResource(
    partnerId: string,
    resource: SubscriptionResource,
    currentCount: number,
  ): Promise<boolean> {
    const limits = await this.getPartnerSubscriptionLimits(partnerId);
    const limit = limits[resource];

    if (limit === 'unlimited') {
      return true;
    }

    return currentCount < limit;
  }

  async getResourceCounts(partnerId: string): Promise<{
    workers: number;
    projects: number;
    managers: number;
  }> {
    const partner = await this.db.query.partners.findFirst({
      where: eq(partners.id, partnerId),
      with: {
        workers: {
          where: (workers, { eq, or }) =>
            or(
              eq(workers.employmentStatus, 'active'),
              eq(workers.employmentStatus, 'quit_notice'),
              eq(workers.employmentStatus, 'terminated_notice'),
            ),
          columns: { id: true },
        },
        projects: {
          where: (projects, { eq }) => eq(projects.isActive, true),
          columns: { id: true },
        },
        managers: {
          where: (managers, { eq, and }) =>
            and(
              eq(managers.employmentStatus, 'active'),
              eq(managers.approvalState, 'approved'),
            ),
          columns: { id: true },
        },
      },
    });

    return {
      workers: partner?.workers?.length || 0,
      projects: partner?.projects?.length || 0,
      managers: partner?.managers?.length || 0,
    };
  }

  async enforceFeatureAvailability(
    partnerId: string,
    feature: SubscriptionFeature,
  ): Promise<void> {
    const isAvailable = await this.isFeatureAvailable(partnerId, feature);

    if (!isAvailable) {
      switch (feature) {
        case SubscriptionFeature.WebVersion:
          throw new WebVersionNotAvailableException();
        case SubscriptionFeature.PhotoConfirmation:
          throw new PhotoConfirmationNotAvailableException();
        case SubscriptionFeature.FinancialStatistics:
          throw new FinancialStatisticsNotAvailableException();
        case SubscriptionFeature.Charts:
          throw new ChartsNotAvailableException();
        case SubscriptionFeature.AdvancedAnalytics:
          throw new AdvancedAnalyticsNotAvailableException();
        case SubscriptionFeature.ReportGeneration:
          throw new ReportGenerationNotAvailableException();
        default:
          throw new FeatureNotAvailableException();
      }
    }
  }

  async enforceResourceLimit(
    partnerId: string,
    resource: SubscriptionResource,
    currentCount?: number,
  ): Promise<void> {
    const counts =
      currentCount !== undefined
        ? { [resource]: currentCount }
        : await this.getResourceCounts(partnerId);

    const canAdd = await this.canAddResource(
      partnerId,
      resource,
      counts[resource],
    );

    if (!canAdd) {
      switch (resource) {
        case SubscriptionResource.Workers:
          throw new WorkersLimitExceededException();
        case SubscriptionResource.Projects:
          throw new ProjectsLimitExceededException();
        case SubscriptionResource.Managers:
          throw new ManagersLimitExceededException();
        default:
          throw new SubscriptionLimitExceededException(resource);
      }
    }
  }

  async getSubscriptionInfo(partnerId: string): Promise<{
    tier: SubscriptionTier;
    limits: SubscriptionLimits;
    usage: {
      workers: number;
      projects: number;
      managers: number;
    };
  }> {
    const [tier, limits, usage] = await Promise.all([
      this.getPartnerSubscriptionTier(partnerId),
      this.getPartnerSubscriptionLimits(partnerId),
      this.getResourceCounts(partnerId),
    ]);

    return {
      tier,
      limits,
      usage,
    };
  }
}
