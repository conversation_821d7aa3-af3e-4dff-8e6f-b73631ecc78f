import { Module, forwardRef } from '@nestjs/common';

import { PartnersModule } from '../partners/partners.module';
import { ManagersModule } from '../managers/managers.module';
import { WorkersModule } from '../workers/workers.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { RegistrationRequestsModule } from '../registration-requests/registration-requests.module';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionExamplesController } from './examples/subscription-examples.controller';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => WorkersModule),
    forwardRef(() => RegistrationCodesModule),
    forwardRef(() => RegistrationRequestsModule),
  ],
  controllers: [SubscriptionController, SubscriptionExamplesController],
  providers: [SubscriptionService],
  exports: [SubscriptionService],
})
export class SubscriptionModule {}
