import { DrizzlePGModule } from '@knaadh/nestjs-drizzle-pg';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as activityHistoryEntity from './entities/activity-history.entity';
import * as credentialVerificationEntity from './entities/credential-verification.entity';
import * as dailyReportEntity from './entities/daily-report.entity';
import * as deviceEntity from './entities/device.entity';
import * as filePermissionEntity from './entities/file-permission.entity';
import * as filesEntity from './entities/file.entity';
import * as managerEntity from './entities/manager.entity';
import * as partnerEntity from './entities/partner.entity';
import * as passwordResetRequestEntity from './entities/password-reset-request.entity';
import * as pauseHistoryEntity from './entities/pause-history.entity';
import * as presenceValidationEntity from './entities/presence-validation.entity';
import * as projectManagerEntity from './entities/project-manager.entity';
import * as projectEntity from './entities/project.entity';
import * as registrationCodeEntity from './entities/registration-code.entity';
import * as registrationRequestEntity from './entities/registration-request.entity';
import * as sessionEntity from './entities/session.entity';
import * as userEntity from './entities/user.entity';
import * as workerEntity from './entities/worker.entity';
import * as enums from './enums';

export const dbSchema = {
  ...enums,
  ...activityHistoryEntity,
  ...credentialVerificationEntity,
  ...dailyReportEntity,
  ...deviceEntity,
  ...filePermissionEntity,
  ...managerEntity,
  ...partnerEntity,
  ...passwordResetRequestEntity,
  ...pauseHistoryEntity,
  ...presenceValidationEntity,
  ...projectEntity,
  ...projectManagerEntity,
  ...registrationCodeEntity,
  ...registrationRequestEntity,
  ...filesEntity,
  ...sessionEntity,
  ...userEntity,
  ...workerEntity,
};

@Module({
  imports: [
    DrizzlePGModule.registerAsync({
      tag: 'DB',
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        return {
          pg: {
            connection: 'pool' as const,
            config: {
              connectionString: configService.get('db.connectionUrl'),
            },
          },
          config: { schema: { ...dbSchema } },
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}

export type Database = NodePgDatabase<typeof dbSchema>;
