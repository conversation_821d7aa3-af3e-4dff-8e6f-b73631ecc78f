import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  numeric,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { reportStatusEnum, userRoleEnum } from '../enums';
import { files } from './file.entity';
import { pauseHistory } from './pause-history.entity';
import { presenceValidations } from './presence-validation.entity';
import { projects } from './project.entity';
import { users } from './user.entity';
import { workers } from './worker.entity';

export const dailyReports = pgTable('daily_report', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  reportDate: date('report_date').notNull(),
  submittedHours: numeric('submitted_hours', { precision: 6, scale: 3 })
    .notNull()
    .default('0.00'),
  approvedHours: numeric('approved_hours', { precision: 6, scale: 3 }),
  endReason: text('end_reason'),
  status: reportStatusEnum('status').notNull().default('pending'),
  isManual: boolean('is_manual').notNull().default(false),
  manualPhotoId: varchar('manual_photo_id', { length: 36 }).references(
    () => files.id,
  ),
  manualReason: text('manual_reason'),
  manualCreatorId: varchar('manual_creator_id', { length: 36 }).references(
    () => users.id,
  ),
  manualCreatorRole: userRoleEnum(),
  onPause: boolean('on_pause').notNull().default(false),
  authorId: varchar('author_id', { length: 36 })
    .references(() => workers.id)
    .notNull(),
  supervisorId: varchar('supervisor_id', { length: 36 }).references(
    () => users.id,
  ),
  projectId: varchar('project_id', { length: 36 })
    .references(() => projects.id)
    .notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  submittedAt: timestamp('submitted_at'),
  updatedAt: timestamp('updated_at')
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const dailyReportsRelations = relations(
  dailyReports,
  ({ one, many }) => ({
    author: one(workers, {
      fields: [dailyReports.authorId],
      references: [workers.id],
    }),
    supervisor: one(users, {
      fields: [dailyReports.supervisorId],
      references: [users.id],
    }),
    presenceValidations: many(presenceValidations),
    pauseHistory: many(pauseHistory),
    project: one(projects, {
      fields: [dailyReports.projectId],
      references: [projects.id],
    }),
    file: one(files, {
      fields: [dailyReports.manualPhotoId],
      references: [files.id],
    }),
  }),
);
