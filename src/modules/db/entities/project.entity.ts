import { relations } from 'drizzle-orm';
import {
  boolean,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { dailyReports } from './daily-report.entity';
import { partners } from './partner.entity';
import { projectManagers } from './project-manager.entity';
import { schedules } from './schedule.entity';
import { workers } from './worker.entity';

export const projects = pgTable('project', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  name: text('name').notNull(),
  description: text('description'),
  address: text('address'),
  scheduleId: varchar('schedule_id', { length: 36 }).references(
    () => schedules.id,
    { onDelete: 'set null' },
  ),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  partnerId: varchar('partner_id', { length: 36 })
    .references(() => partners.id)
    .notNull(),
  isActive: boolean('is_active').notNull().default(true),
});

export const projectsRelations = relations(projects, ({ one, many }) => ({
  workers: many(workers),
  partner: one(partners, {
    fields: [projects.partnerId],
    references: [partners.id],
  }),
  schedule: one(schedules, {
    fields: [projects.scheduleId],
    references: [schedules.id],
  }),
  dailyReports: many(dailyReports),
  projectManagers: many(projectManagers),
}));
