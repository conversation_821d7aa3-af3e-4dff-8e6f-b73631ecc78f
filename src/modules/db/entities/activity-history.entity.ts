import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { activityTypeEnum } from '../enums';
import { managers } from './manager.entity';
import { partners } from './partner.entity';
import { projects } from './project.entity';
import { users } from './user.entity';
import { workers } from './worker.entity';

export const activityHistory = pgTable('activity_history', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  activityType: activityTypeEnum('activity_type').notNull(),
  operationAuthorId: varchar('operation_author', { length: 36 })
    .notNull()
    .references(() => users.id, { onDelete: 'set null' }),
  workerId: varchar('worker_id', { length: 36 }).references(() => workers.id),
  managerId: varchar('manager_id', { length: 36 }).references(
    () => managers.id,
  ),
  projectId: varchar('project_id', { length: 36 }).references(
    () => projects.id,
  ),
  partnerId: varchar('partner_id', { length: 36 })
    .notNull()
    .references(() => partners.id),
  metadata: text('metadata'),
  happenedAt: timestamp('happened_at').notNull().defaultNow(),
});

export const activityHistoryRelations = relations(
  activityHistory,
  ({ one }) => ({
    operationAuthor: one(users, {
      fields: [activityHistory.operationAuthorId],
      references: [users.id],
    }),
    worker: one(workers, {
      fields: [activityHistory.workerId],
      references: [workers.id],
    }),
    partner: one(partners, {
      fields: [activityHistory.partnerId],
      references: [partners.id],
    }),
    manager: one(managers, {
      fields: [activityHistory.managerId],
      references: [managers.id],
    }),
    project: one(projects, {
      fields: [activityHistory.projectId],
      references: [projects.id],
    }),
  }),
);
