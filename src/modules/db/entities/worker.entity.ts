import { relations } from 'drizzle-orm';
import {
  numeric,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import {
  approvalStatusEnum,
  employmentStatusEnum,
  validatedPresenceStatusEnum,
  workingStatusEnum,
} from '../enums';
import { dailyReports } from './daily-report.entity';
import { daysOff } from './day-off.entity';
import { managers } from './manager.entity';
import { partners } from './partner.entity';
import { professions } from './profession.entity';
import { projects } from './project.entity';
import { registrationRequests } from './registration-request.entity';
import { schedules } from './schedule.entity';
import { users } from './user.entity';

export const workers = pgTable('worker', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  userId: varchar('user_id', { length: 36 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  professionId: varchar('profession_id', { length: 36 }).references(
    () => professions.id,
  ),
  scheduleId: varchar('schedule_id', { length: 36 }).references(
    () => schedules.id,
  ),
  managerId: varchar('manager_id', { length: 36 }).references(
    () => managers.id,
  ),
  partnerId: varchar('partner_id', { length: 36 })
    .references(() => partners.id)
    .notNull(),
  hourlyRate: numeric('hourly_rate', { precision: 10, scale: 2 }),
  approvalState: approvalStatusEnum('approval_state').default('pending'),
  workingStatus: workingStatusEnum('working_status').default('passive'),
  validatedPresenceStatus: validatedPresenceStatusEnum(
    'validated_presence_status',
  ),
  employmentStatus: employmentStatusEnum('employment_status')
    .notNull()
    .default('inactive'),
  endEmploymentDate: timestamp('end_employment_date'),
  endEmploymentReason: text('end_employment_reason'),
  profession: varchar('profession', { length: 100 }),
  projectId: varchar('project_id', { length: 36 }).references(
    () => projects.id,
    {
      onDelete: 'set null',
    },
  ),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  checkedPresenceAt: timestamp('checked_presence_at'),
});

export const workersRelations = relations(workers, ({ one, many }) => ({
  user: one(users, { fields: [workers.userId], references: [users.id] }),
  manager: one(managers, {
    fields: [workers.managerId],
    references: [managers.id],
  }),
  partner: one(partners, {
    fields: [workers.partnerId],
    references: [partners.id],
  }),
  daysOff: many(daysOff),
  schedule: one(schedules, {
    fields: [workers.scheduleId],
    references: [schedules.id],
  }),
  profession: one(professions, {
    fields: [workers.professionId],
    references: [professions.id],
  }),
  registrationRequests: many(registrationRequests),
  project: one(projects, {
    fields: [workers.projectId],
    references: [projects.id],
  }),
  dailyReports: many(dailyReports),
}));
