import { relations } from 'drizzle-orm';
import {
  boolean,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { reminderTimeEnum } from '../enums';
import { partners } from './partner.entity';
import { projects } from './project.entity';
import { scheduleDays } from './schedule-day.entity';
import { workers } from './worker.entity';

export const schedules = pgTable('schedule', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  partnerId: varchar('partner_id', { length: 36 })
    .notNull()
    .references(() => partners.id),
  requirePhotoValidation: boolean('require_photo_validation')
    .notNull()
    .default(true),
  reminderTime: reminderTimeEnum('reminder_time')
    .notNull()
    .default('schedule_start_and_end'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  isActive: boolean('is_active').default(true),
});

export const schedulesRelations = relations(schedules, ({ one, many }) => ({
  partner: one(partners, {
    fields: [schedules.partnerId],
    references: [partners.id],
  }),
  project: one(projects, {
    fields: [schedules.id],
    references: [projects.scheduleId],
  }),
  scheduleDays: many(scheduleDays),
  workers: many(workers),
}));
