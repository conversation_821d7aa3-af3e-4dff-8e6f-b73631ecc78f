import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { countriesEnum, genderEnum, userRoleEnum } from '../enums';
import { credentialVerifications } from './credential-verification.entity';
import { dailyReports } from './daily-report.entity';
import { daysOff } from './day-off.entity';
import { devices } from './device.entity';
import { filePermissions } from './file-permission.entity';
import { files } from './file.entity';
import { managers } from './manager.entity';
import { partners } from './partner.entity';
import { passwordResetRequests } from './password-reset-request.entity';
import { presenceValidations } from './presence-validation.entity';
import { registrationRequests } from './registration-request.entity';
import { sessions } from './session.entity';
import { workers } from './worker.entity';

export const users = pgTable('user', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  avatarId: varchar('avatar_id', { length: 36 }).references(() => files.id),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 100 }).unique().notNull(),
  phoneNumber: varchar('phone_number', { length: 20 }).notNull(),
  birthDate: date('birth_date'),
  gender: genderEnum('gender'),
  role: userRoleEnum('role').notNull(),
  countryOfResidence: countriesEnum('country_of_residence'),
  citizenship: countriesEnum('citizenship').notNull(),
  documentScan: text('document_scan'),
  isEmailVerified: boolean('is_email_verified').notNull().default(false),
  isPhoneVerified: boolean('is_phone_verified').notNull().default(false),
  hashedPassword: varchar('hashed_password', { length: 128 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

export const usersRelations = relations(users, ({ many }) => ({
  partners: many(partners),
  managers: many(managers),
  workers: many(workers),
  approvedRegistrationRequests: many(registrationRequests),
  daysOffRequests: many(daysOff),
  checkedDailyReports: many(dailyReports),
  presenceValidations: many(presenceValidations),
  devices: many(devices),
  passwordResetRequests: many(passwordResetRequests),
  sessions: many(sessions),
  credentialVerifications: many(credentialVerifications),
  filePermissions: many(filePermissions),
}));
