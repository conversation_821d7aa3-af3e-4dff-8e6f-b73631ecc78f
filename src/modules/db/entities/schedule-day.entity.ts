import { relations } from 'drizzle-orm';
import { boolean, json, pgTable, time, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { schedules } from './schedule.entity';
import { dayOfWeekEnum } from '../enums';
// import { workLocations } from './work-location.entity';

export const scheduleDays = pgTable('schedule_day', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  scheduleId: varchar('schedule_id', { length: 36 })
    .notNull()
    .references(() => schedules.id),
  dayOfWeek: dayOfWeekEnum('day_of_week').notNull(),
  isWorkingDay: boolean('is_working_day').notNull().default(true),
  startTime: time('start_time'),
  endTime: time('end_time'),
  breaks: json('breaks').$type<ScheduleDayBreak[]>().default([]),
  // workLocationId: varchar('work_location_id', { length: 36 }).references(
  //   () => workLocations.id,
  // ),
});

export interface ScheduleDayBreak {
  id: string;
  name: string;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
}

export const scheduleDaysRelations = relations(scheduleDays, ({ one }) => ({
  schedule: one(schedules, {
    fields: [scheduleDays.scheduleId],
    references: [schedules.id],
  }),
  // workLocation: one(workLocations, {
  //   fields: [scheduleDays.workLocationId],
  //   references: [workLocations.id],
  // }),
}));
