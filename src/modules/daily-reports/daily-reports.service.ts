import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { differenceInSeconds, isSameMonth } from 'date-fns';
import {
  aliasedTable,
  and,
  asc,
  desc,
  eq,
  gte,
  inArray,
  isNotNull,
  like,
  lte,
  or,
  sql,
} from 'drizzle-orm';

import { Role } from '@/common/enums';
import {
  ActiveReportExistsException,
  DailyReportNotFoundException,
  EmptyArrayException,
  ForbiddenToCreateManualReportException,
  ForbiddenToFinishReportException,
  ManagerNotFoundException,
  NoActiveReportException,
  PartnerNotFoundException,
  PauseHistoryNotFoundException,
  ProjectNotManagedByManagerException,
  SubmittedTimeMismatchException,
  SupervisorMismatchException,
  SupervisorNotFoundException,
  WorkerAlreadyActiveException,
  WorkerAlreadyFinishedException,
  WorkerAlreadyPausedException,
  WorkerNotApprovedException,
  WorkerNotAssignedToProjectException,
  WorkerNotEmployedException,
  WorkerNotFoundException,
} from '@/common/exceptions/custom-exceptions';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { Database } from '../db/db.module';
import { dailyReports } from '../db/entities/daily-report.entity';
import { presenceValidations } from '../db/entities/presence-validation.entity';
import { projectManagers } from '../db/entities/project-manager.entity';
import { projects } from '../db/entities/project.entity';
import { users } from '../db/entities/user.entity';
import { workers } from '../db/entities/worker.entity';
import { PauseHistoryService } from '../pause-history/pause-history.service';
import { PresenceValidationsService } from '../presence-validations/presence-validations.service';
import { UpdatesGateway } from '../updates/updates.gateway';
import { UpdatesEvents } from '../updates/updates.types';
import { WorkersService } from '../workers/workers.service';
import { ChangeApprovedHoursDto } from './dto/change-approved-hours.dto';
import { ChangePauseDto } from './dto/change-pause.dto';
import { CreateDailyReportDto } from './dto/create-daily-report.dto';
import { CreateManualReportDto } from './dto/create-manual-report.dto';
import { ReportFilterParamsDto } from './dto/daily-reports-filter-params.dto';
import { FinishReportDto } from './dto/finish-report.dto';
import { StartReportDto } from './dto/start-report.dto';
import { UpdateDailyReportDto } from './dto/update-daily-report.dto';
import { ManagersService } from '../managers/managers.service';

@Injectable()
export class DailyReportsService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => PresenceValidationsService))
    private readonly presenceValidationsService: Forwarded<PresenceValidationsService>,
    private readonly pauseHistoryService: PauseHistoryService,
    @Inject(forwardRef(() => UpdatesGateway))
    private readonly updatesGateway: Forwarded<UpdatesGateway>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
  ) {}

  async create(createDailyReportDto: CreateDailyReportDto) {
    return await this.db.transaction(async (tx) => {
      const worker = await tx.query.workers.findFirst({
        where: (workers, { eq }) =>
          eq(workers.id, createDailyReportDto.authorId),
      });

      if (!worker) {
        throw new WorkerNotFoundException();
      }

      const [report] = await tx
        .insert(dailyReports)
        .values(createDailyReportDto)
        .returning({
          id: dailyReports.id,
        });

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.DAILY_REPORT_CREATED,
        payload: {
          reportId: report.id,
          workerId: createDailyReportDto.authorId,
          projectId: createDailyReportDto.projectId,
        },
      });

      return report;
    });
  }

  async findAll(
    requestUser: RequestUserType,
    filterParams?: ReportFilterParamsDto,
  ) {
    const { whereClauses, orderBy } = this.generateFilterClauses(filterParams);
    const userWhereClauses =
      requestUser.role === 'partner'
        ? eq(workers.partnerId, requestUser.entityId)
        : requestUser.role === 'worker'
          ? eq(workers.id, requestUser.entityId)
          : undefined;

    let managerPermissionType: ManagerPermissionType | undefined = undefined;
    let partnerId: string | undefined =
      requestUser.role === 'partner' ? requestUser.entityId : undefined;
    if (requestUser.role === 'manager') {
      const manager = await this.managersService.findOne(requestUser.entityId);
      if (!manager) throw new ManagerNotFoundException();
      managerPermissionType = (manager.permissionType ??
        'project_manager') as ManagerPermissionType;
      partnerId = manager.partnerId;
    }
    const supervisorUser = aliasedTable(users, 'supervisor');

    const reports = await this.db
      .select({
        id: dailyReports.id,
        reportDate: dailyReports.reportDate,
        submittedHours: dailyReports.submittedHours,
        approvedHours: dailyReports.approvedHours,
        status: dailyReports.status,
        isManual: dailyReports.isManual,
        manualPhotoId: dailyReports.manualPhotoId,
        createdAt: dailyReports.createdAt,
        endReason: dailyReports.endReason,
        manualReason: dailyReports.manualReason,
        manualCreatorId: dailyReports.manualCreatorId,
        manualCreatorRole: dailyReports.manualCreatorRole,
        authorFullName: sql<string>`
      ${users.firstName} || ' ' || ${users.lastName}
    `,
      })
      .from(dailyReports)
      .leftJoin(workers, eq(dailyReports.authorId, workers.id))
      .leftJoin(
        projectManagers,
        eq(dailyReports.projectId, projectManagers.projectId),
      )
      .leftJoin(users, eq(workers.userId, users.id))
      .leftJoin(
        supervisorUser,
        eq(dailyReports.supervisorId, supervisorUser.id),
      )
      .where(
        and(
          ...whereClauses,
          userWhereClauses,
          managerPermissionType === ManagerPermissionType.ProjectManager
            ? eq(projectManagers.managerId, requestUser.entityId)
            : undefined,
          partnerId ? eq(workers.partnerId, partnerId) : undefined,
        ),
      )
      .orderBy(orderBy);

    const reportsWithPresenceValidations =
      await this.db.query.dailyReports.findMany({
        where: (dailyReports, { eq, or }) =>
          or(...reports.map((report) => eq(dailyReports.id, report.id))),
        columns: {
          id: true,
        },
        with: {
          presenceValidations: {
            where: (presenceValidations, { eq, and, isNotNull }) =>
              and(
                eq(presenceValidations.requiresPhoto, true),
                isNotNull(presenceValidations.photoId),
              ),
            columns: {
              photoId: true,
            },
          },
        },
      });

    return reports.map((report) => {
      const presenceValidation = reportsWithPresenceValidations.find(
        (reportWithPresenceValidation) =>
          reportWithPresenceValidation.id === report.id,
      );
      const validations =
        presenceValidation?.presenceValidations.filter((p) => !!p.photoId) ??
        [];
      return {
        ...report,
        photoAmount: validations.length,
        photos: validations
          .map((presenceValidation) => presenceValidation.photoId)
          .filter(Boolean) as string[],
      };
    });
  }

  async findOne(id: string) {
    const report = await this.db.query.dailyReports.findFirst({
      where: (dailyReports, { eq }) => eq(dailyReports.id, id),
      with: {
        presenceValidations: {
          orderBy: (presenceValidations, { asc }) =>
            asc(presenceValidations.createdAt),
        },
      },
    });
    return report;
  }

  async findOneWithWorker(id: string) {
    const report = await this.db.query.dailyReports.findFirst({
      where: (dailyReports, { eq }) => eq(dailyReports.id, id),
      with: {
        author: {
          with: {
            user: {
              columns: {
                hashedPassword: false,
              },
            },
          },
        },
      },
    });
    return report;
  }

  async findActiveByReportDate(workerId: string, reportDate: string) {
    return this.db.query.dailyReports.findFirst({
      where: (dailyReports, { eq, and }) =>
        and(
          eq(dailyReports.authorId, workerId),
          eq(dailyReports.reportDate, reportDate),
          eq(dailyReports.status, 'pending'),
        ),
      with: {
        author: {
          with: {
            user: {
              columns: {
                hashedPassword: false,
              },
            },
          },
        },
      },
    });
  }

  async findActiveByWorkerId(workerId: string) {
    return (
      await this.db
        .select()
        .from(dailyReports)
        .where(
          and(
            eq(dailyReports.authorId, workerId),
            eq(dailyReports.status, 'pending'),
          ),
        )
        .orderBy(desc(dailyReports.createdAt))
        .limit(1)
    )?.[0];
  }

  async findActiveByWorkerIds(workerIds: string[]) {
    const supervisorUser = aliasedTable(users, 'supervisor');
    return this.db
      .select({
        id: dailyReports.id,
        reportDate: dailyReports.reportDate,
        submittedHours: dailyReports.submittedHours,
        approvedHours: dailyReports.approvedHours,
        status: dailyReports.status,
        isManual: dailyReports.isManual,
        onPause: dailyReports.onPause,
        manualPhotoId: dailyReports.manualPhotoId,
        createdAt: dailyReports.createdAt,
        authorId: dailyReports.authorId,
        authorFullName: sql<string>`
      ${users.firstName} || ' ' || ${users.lastName}
    `,
      })
      .from(dailyReports)
      .leftJoin(workers, eq(dailyReports.authorId, workers.id))
      .leftJoin(users, eq(workers.userId, users.id))
      .leftJoin(
        supervisorUser,
        eq(dailyReports.supervisorId, supervisorUser.id),
      )
      .where(
        and(
          or(
            ...workerIds.map((workerId) => eq(dailyReports.authorId, workerId)),
          ),
          eq(dailyReports.status, 'pending'),
        ),
      )
      .orderBy(dailyReports.reportDate);
  }

  async findAllByWorkerId(
    workerId: string,
    filterParams?: ReportFilterParamsDto,
  ) {
    const { whereClauses, orderBy } = this.generateFilterClauses(filterParams);

    const supervisorUser = aliasedTable(users, 'supervisor');

    const reports = await this.db
      .select({
        id: dailyReports.id,
        reportDate: dailyReports.reportDate,
        submittedHours: dailyReports.submittedHours,
        approvedHours: dailyReports.approvedHours,
        status: dailyReports.status,
        isManual: dailyReports.isManual,
        manualPhotoId: dailyReports.manualPhotoId,
        createdAt: dailyReports.createdAt,
        endReason: dailyReports.endReason,
        manualReason: dailyReports.manualReason,
        manualCreatorId: dailyReports.manualCreatorId,
        manualCreatorRole: dailyReports.manualCreatorRole,
        authorFullName: sql<string>`
      ${users.firstName} || ' ' || ${users.lastName}
    `,
      })
      .from(dailyReports)
      .leftJoin(workers, eq(dailyReports.authorId, workers.id))
      .leftJoin(users, eq(workers.userId, users.id))
      .leftJoin(
        supervisorUser,
        eq(dailyReports.supervisorId, supervisorUser.id),
      )
      .where(and(...whereClauses, eq(workers.id, workerId)))
      .orderBy(orderBy);

    const reportsWithPresenceValidations =
      await this.db.query.dailyReports.findMany({
        where: (dailyReports, { eq, or }) =>
          or(...reports.map((report) => eq(dailyReports.id, report.id))),
        columns: {
          id: true,
        },
        with: {
          presenceValidations: {
            where: (presenceValidations, { eq, and, isNotNull }) =>
              and(
                eq(presenceValidations.requiresPhoto, true),
                isNotNull(presenceValidations.photoId),
              ),
            columns: {
              photoId: true,
            },
          },
        },
      });
    return reports.map((report) => {
      const presenceValidation = reportsWithPresenceValidations.find(
        (reportWithPresenceValidation) =>
          reportWithPresenceValidation.id === report.id,
      );
      const validations =
        presenceValidation?.presenceValidations.filter((p) => !!p.photoId) ??
        [];
      return {
        ...report,
        photoAmount: validations.length,
        photos: validations
          .map((presenceValidation) => presenceValidation.photoId)
          .filter(Boolean) as string[],
      };
    });
  }

  async findAllByProjectId(
    projectId: string,
    filterParams?: ReportFilterParamsDto,
  ) {
    const { whereClauses, orderBy } = this.generateFilterClauses(filterParams);

    const supervisorUser = aliasedTable(users, 'supervisor');
    const reports = await this.db
      .select({
        id: dailyReports.id,
        reportDate: dailyReports.reportDate,
        submittedHours: dailyReports.submittedHours,
        approvedHours: dailyReports.approvedHours,
        status: dailyReports.status,
        isManual: dailyReports.isManual,
        manualPhotoId: dailyReports.manualPhotoId,
        createdAt: dailyReports.createdAt,
        endReason: dailyReports.endReason,
        manualReason: dailyReports.manualReason,
        manualCreatorId: dailyReports.manualCreatorId,
        manualCreatorRole: dailyReports.manualCreatorRole,
        authorFullName: sql<string>`
      ${users.firstName} || ' ' || ${users.lastName}
    `,
      })
      .from(dailyReports)
      .leftJoin(workers, eq(dailyReports.authorId, workers.id))
      .leftJoin(users, eq(workers.userId, users.id))
      .leftJoin(
        supervisorUser,
        eq(dailyReports.supervisorId, supervisorUser.id),
      )
      .leftJoin(projects, eq(dailyReports.projectId, projects.id))
      .where(and(...whereClauses, eq(projects.id, projectId)))
      .orderBy(orderBy);

    const reportsWithPresenceValidations =
      await this.db.query.dailyReports.findMany({
        where: (dailyReports, { eq, or }) =>
          or(...reports.map((report) => eq(dailyReports.id, report.id))),
        columns: {
          id: true,
        },
        with: {
          presenceValidations: {
            where: (presenceValidations, { eq, and, isNotNull }) =>
              and(
                eq(presenceValidations.requiresPhoto, true),
                isNotNull(presenceValidations.photoId),
              ),
            columns: {
              photoId: true,
            },
          },
        },
      });
    return reports.map((report) => {
      const presenceValidation = reportsWithPresenceValidations.find(
        (reportWithPresenceValidation) =>
          reportWithPresenceValidation.id === report.id,
      );
      const validations =
        presenceValidation?.presenceValidations.filter((p) => !!p.photoId) ??
        [];
      return {
        ...report,
        photoAmount: validations.length,
        photos: validations
          .map((presenceValidation) => presenceValidation.photoId)
          .filter(Boolean) as string[],
      };
    });
  }

  findAllActiveByProjectId(projectId: string) {
    const currentDate = new Date().toISOString().split('T')[0];
    const supervisorUser = aliasedTable(users, 'supervisor');
    return this.db
      .select({
        id: dailyReports.id,
        reportDate: dailyReports.reportDate,
        submittedHours: dailyReports.submittedHours,
        approvedHours: dailyReports.approvedHours,
        status: dailyReports.status,
        onPause: dailyReports.onPause,
        isManual: dailyReports.isManual,
        manualPhotoId: dailyReports.manualPhotoId,
        authorId: dailyReports.authorId,
        supervisorId: dailyReports.supervisorId,
        createdAt: dailyReports.createdAt,
        submittedAt: dailyReports.submittedAt,
        updatedAt: dailyReports.updatedAt,
        endReason: dailyReports.endReason,
        manualReason: dailyReports.manualReason,
        manualCreatorId: dailyReports.manualCreatorId,
        manualCreatorRole: dailyReports.manualCreatorRole,
        authorFullName: sql<string>`
          ${users.firstName} || ' ' || ${users.lastName}
          `,
        supervisorFullName: sql<string>`
          ${supervisorUser.firstName} || ' ' || ${supervisorUser.lastName}
        `,
      })
      .from(dailyReports)
      .leftJoin(workers, eq(dailyReports.authorId, workers.id))
      .leftJoin(users, eq(workers.userId, users.id))
      .leftJoin(
        supervisorUser,
        eq(dailyReports.supervisorId, supervisorUser.id),
      )
      .leftJoin(projects, eq(workers.projectId, projects.id))
      .where(
        and(
          eq(projects.id, projectId),
          eq(dailyReports.status, 'pending'),
          or(
            eq(dailyReports.reportDate, sql`${currentDate}`),
            gte(
              dailyReports.createdAt,
              sql`(SELECT MAX(${dailyReports.createdAt}) FROM ${dailyReports})`,
            ),
          ),
        ),
      );
  }

  async calculateApprovedHoursForWorker(workerId: string, date: string) {
    const targetDate = new Date(date);
    const reports = await this.findAllByWorkerId(workerId, {
      status: 'approved',
    });

    const thisMonthHours = reports
      .filter(({ reportDate }) => isSameMonth(reportDate, targetDate))
      .reduce(
        (acc, { approvedHours }) => acc + (Number(approvedHours) || 0),
        0,
      );

    return thisMonthHours;
  }

  update(id: string, updateDailyReportDto: UpdateDailyReportDto) {
    return this.db.transaction(async (tx) => {
      const dailyReport = await tx.query.dailyReports.findFirst({
        where: (dailyReports, { eq }) => eq(dailyReports.id, id),
      });

      if (!dailyReport) {
        throw new DailyReportNotFoundException();
      }

      return await tx
        .update(dailyReports)
        .set(updateDailyReportDto)
        .where(eq(dailyReports.id, id));
    });
  }

  async changePauseState(
    id: string,
    changePauseDto: ChangePauseDto,
    workerId: string,
  ) {
    const worker = await this.workersService.findOne(workerId);
    if (!worker) throw new WorkerNotFoundException();
    if (changePauseDto.onPause && worker.workingStatus === 'paused')
      throw new WorkerAlreadyPausedException();
    if (!changePauseDto.onPause && worker.workingStatus === 'started')
      throw new WorkerAlreadyActiveException();

    const { id: presenceValidationId } =
      await this.presenceValidationsService.createForWorker(
        workerId,
        {
          requiresGeo: true,
          requiresPhoto: false,
          reason: changePauseDto.onPause ? 'pause' : 'unpause',
        },
        worker.partner.user.id,
        id,
        false,
        false,
      );

    if (changePauseDto.onPause) {
      await this.workersService.changeWorkingStatus(workerId, 'paused');
      await this.pauseHistoryService.create({
        dailyReportId: id,
        pauseStart: new Date(),
      });
    } else {
      await this.workersService.changeWorkingStatus(workerId, 'started');
      const pauseHistory =
        await this.pauseHistoryService.findActiveByReportId(id);
      if (!pauseHistory) throw new PauseHistoryNotFoundException();
      await this.pauseHistoryService.update(pauseHistory.id, {
        pauseEnd: new Date(),
      });
    }

    await this.db
      .update(dailyReports)
      .set({ onPause: changePauseDto.onPause })
      .where(and(eq(dailyReports.id, id), eq(dailyReports.authorId, workerId)));

    return {
      presenceValidationId,
    };
  }

  async startReport(startReportDto: StartReportDto, workerId: string) {
    const existingReport = await this.findActiveByWorkerId(workerId);
    if (existingReport) {
      throw new ActiveReportExistsException();
    }

    const worker = await this.workersService.findOne(workerId);
    if (!worker) throw new WorkerNotFoundException();
    if (
      !['active', 'quit_notice', 'terminated_notice'].includes(
        worker.employmentStatus,
      )
    )
      throw new WorkerNotEmployedException();
    if (worker.approvalState !== 'approved')
      throw new WorkerNotApprovedException();
    if (!worker.projectId) throw new WorkerNotAssignedToProjectException();

    const { id } = await this.create({
      ...startReportDto,
      authorId: workerId,
      supervisorId: worker.partner.user.id,
      projectId: worker.projectId,
    });

    const { id: presenceValidationId } =
      await this.presenceValidationsService.createForWorker(
        workerId,
        {
          requiresGeo: true,
          requiresPhoto: false,
          reason: 'start',
        },
        worker.partner.user.id,
        id,
        false,
        false,
      );

    await this.workersService.changeWorkingStatus(workerId, 'started');

    return { id, presenceValidationId };
  }

  async finishReport(
    id: string,
    finishReportDto: FinishReportDto,
    user: RequestUserType,
  ) {
    if (user.role === 'partner' || user.role === 'manager') {
      const report = await this.findOneWithWorker(id);
      if (!report) throw new DailyReportNotFoundException();
      let partnerId = user.role === 'partner' ? user.entityId : undefined;
      if (user.role === 'manager') {
        partnerId = await this.managersService.getPartnerId(user.entityId);
      }
      if (report.author.partnerId !== partnerId)
        throw new ForbiddenToFinishReportException();

      return this.finishReportInternal(
        id,
        finishReportDto,
        report.authorId,
        false,
      );
    }

    return this.finishReportInternal(id, finishReportDto, user.entityId);
  }

  private async finishReportInternal(
    id: string,
    finishReportDto: FinishReportDto,
    authorId: string,
    validateAuthor: boolean = true,
  ) {
    const worker = await this.workersService.findOne(authorId);
    if (!worker) throw new WorkerNotFoundException();
    if (worker.workingStatus === 'finished')
      throw new WorkerAlreadyFinishedException();

    const { totalSeconds } = await this.calculateValidWorkingTime(authorId);

    if (finishReportDto.submittedHours) {
      const submittedSeconds = finishReportDto.submittedHours * 3600;
      const tolerance = 300;
      if (Math.abs(submittedSeconds - totalSeconds) > tolerance) {
        throw new SubmittedTimeMismatchException();
      }
    }

    const calculatedHours = totalSeconds / 3600;

    let presenceValidationId: string | undefined;
    if (validateAuthor) {
      const { id: createdPresenceValidationId } =
        await this.presenceValidationsService.createForWorker(
          authorId,
          {
            requiresGeo: true,
            requiresPhoto: false,
            reason: 'finish',
          },
          worker.partner.user.id,
          id,
          false,
          false,
        );
      presenceValidationId = createdPresenceValidationId;
    }

    await this.workersService.changeWorkingStatus(authorId, 'finished');
    await this.db
      .update(dailyReports)
      .set({
        submittedAt: new Date(),
        status: 'submitted',
        submittedHours: calculatedHours.toFixed(2).toString(),
        endReason: validateAuthor ? undefined : finishReportDto.endReason,
      })
      .where(and(eq(dailyReports.id, id), eq(dailyReports.authorId, authorId)));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.DAILY_REPORT_FINISHED,
      payload: {
        reportId: id,
        workerId: authorId,
      },
    });

    return {
      presenceValidationId,
    };
  }

  async changeReportStatus(
    ids: string | string[],
    user: RequestUserType,
    status: 'approved' | 'declined',
    changeApprovedHours?: ChangeApprovedHoursDto,
  ) {
    if (Array.isArray(ids) && !ids.length) throw new EmptyArrayException();
    let partnerUserId = user.role === Role.Partner ? user.id : undefined;
    let managedProjectsIds: string[] | undefined = undefined;
    let managerPermissionType: ManagerPermissionType | undefined = undefined;
    if (!partnerUserId && user.role === Role.Manager) {
      const manager = await this.managersService.findOneWithPartner(
        user.entityId,
      );
      if (!manager) throw new ManagerNotFoundException();
      partnerUserId = manager.partner.userId;
      managerPermissionType = (manager.permissionType ??
        'project_manager') as ManagerPermissionType;
      const managedProjects = await this.managersService.getManagedProjects(
        user.entityId,
      );
      managedProjectsIds = managedProjects.map((project) => project.id);
    }
    if (!partnerUserId) {
      throw new SupervisorNotFoundException();
    }

    const reports = await this.db.query.dailyReports.findMany({
      where: (dailyReports, { eq, or }) =>
        or(
          ...(Array.isArray(ids)
            ? ids.map((id) => eq(dailyReports.id, id))
            : [eq(dailyReports.id, ids)]),
        ),
      columns: {
        id: true,
        supervisorId: true,
        projectId: true,
      },
    });
    if (reports.some((report) => report.supervisorId !== partnerUserId)) {
      throw new SupervisorMismatchException();
    }

    if (
      user.role === Role.Manager &&
      managerPermissionType === ManagerPermissionType.ProjectManager &&
      managedProjectsIds &&
      reports.some((report) => !managedProjectsIds.includes(report.projectId))
    ) {
      throw new ProjectNotManagedByManagerException();
    }

    await this.db
      .update(dailyReports)
      .set({
        status: status,
        supervisorId: partnerUserId,
        approvedHours:
          status === 'approved'
            ? (changeApprovedHours?.approvedHours?.toFixed(2).toString() ??
              sql`daily_report.submitted_hours`)
            : undefined,
      })
      .where(
        and(
          inArray(dailyReports.id, Array.isArray(ids) ? ids : [ids]),
          eq(dailyReports.supervisorId, partnerUserId),
        ),
      );

    const reportIds = Array.isArray(ids) ? ids : [ids];
    for (const reportId of reportIds) {
      const report = await this.db.query.dailyReports.findFirst({
        where: (dailyReports, { eq }) => eq(dailyReports.id, reportId),
        columns: {
          authorId: true,
          projectId: true,
        },
      });
      if (report) {
        await this.updatesGateway.sendMessage({
          type: UpdatesEvents.DAILY_REPORT_UPDATED,
          payload: {
            reportId,
            workerId: report.authorId,
            projectId: report.projectId,
            status,
          },
        });
      }
    }
  }

  async createManualReport(
    createManualReportDto: CreateManualReportDto,
    workerId: string,
    creator: RequestUserType,
  ) {
    const { photoId, submittedHours, manualReason, endReason, ...rest } =
      createManualReportDto;
    let partnerId =
      creator.role === Role.Partner ? creator.entityId : undefined;
    if (!partnerId && creator.role === Role.Manager) {
      partnerId = await this.managersService.getPartnerId(creator.entityId);
    }
    if (creator.role === Role.Worker) {
      partnerId = await this.workersService.getPartnerId(workerId);
    }

    if (!partnerId) {
      throw new PartnerNotFoundException();
    }

    let managedProjectsIds: string[] | undefined = undefined;
    let managerPermissionType: ManagerPermissionType | undefined = undefined;
    if (creator.role === Role.Manager) {
      const manager = await this.managersService.findOne(creator.entityId);
      if (!manager) throw new ManagerNotFoundException();
      managerPermissionType = (manager.permissionType ??
        'project_manager') as ManagerPermissionType;
      if (managerPermissionType !== ManagerPermissionType.All) {
        const managedProjects = await this.managersService.getManagedProjects(
          creator.entityId,
        );
        managedProjectsIds = managedProjects.map((project) => project.id);
      }
    }

    const worker = await this.workersService.findOne(workerId);
    if (!worker || worker.partnerId !== partnerId)
      throw new WorkerNotFoundException();
    if (
      !['active', 'quit_notice', 'terminated_notice'].includes(
        worker.employmentStatus,
      )
    )
      throw new WorkerNotEmployedException();
    if (worker.approvalState !== 'approved')
      throw new WorkerNotApprovedException();
    if (!worker.projectId) throw new WorkerNotAssignedToProjectException();
    if (
      creator.role === Role.Manager &&
      managerPermissionType === ManagerPermissionType.ProjectManager &&
      managedProjectsIds &&
      !managedProjectsIds.includes(worker.projectId)
    ) {
      throw new ForbiddenToCreateManualReportException();
    }

    return this.create({
      ...rest,
      submittedHours: submittedHours.toString(),
      manualPhotoId: photoId,
      isManual: true,
      authorId: workerId,
      supervisorId: worker.partner.user.id,
      projectId: worker.projectId,
      status: 'submitted',
      endReason,
      manualReason,
      manualCreatorId: creator.id,
      manualCreatorRole: creator.role,
    });
  }

  private generateFilterClauses(filterParams?: ReportFilterParamsDto) {
    const whereClauses = [
      filterParams?.status
        ? inArray(
            dailyReports.status,
            Array.isArray(filterParams.status)
              ? filterParams.status
              : [filterParams.status],
          )
        : undefined,
      filterParams?.dateFrom
        ? gte(dailyReports.reportDate, filterParams.dateFrom)
        : undefined,
      filterParams?.dateTo
        ? lte(dailyReports.reportDate, filterParams.dateTo)
        : undefined,
      filterParams?.workerFullName
        ? or(
            like(users.firstName, filterParams.workerFullName),
            like(users.lastName, filterParams.workerFullName),
          )
        : undefined,
      filterParams?.includesPhoto
        ? isNotNull(presenceValidations.photoId)
        : undefined,
      filterParams?.includesGeo
        ? isNotNull(presenceValidations.geoCoordinates)
        : undefined,
    ];

    const sortBy = filterParams?.sortBy
      ? filterParams.sortBy === 'date'
        ? dailyReports.createdAt
        : filterParams.sortBy === 'submittedHours'
          ? dailyReports.submittedHours
          : filterParams.sortBy === 'approvedHours'
            ? dailyReports.approvedHours
            : filterParams.sortBy === 'status'
              ? dailyReports.status
              : dailyReports.createdAt
      : dailyReports.createdAt;

    const orderBy =
      filterParams?.sortOrder && sortBy
        ? filterParams.sortOrder === 'asc'
          ? asc(sortBy)
          : desc(sortBy)
        : desc(dailyReports.createdAt);

    return { whereClauses, orderBy };
  }

  async calculateValidWorkingTime(workerId: string) {
    const report = await this.findActiveByWorkerId(workerId);
    if (!report) {
      throw new NoActiveReportException();
    }

    const pauseHistory = await this.pauseHistoryService.findAllByReportId(
      report.id,
    );
    const now = new Date();

    const completedPauses = pauseHistory
      .filter((pause) => pause.pauseStart && pause.pauseEnd)
      .map((pause) => ({
        start: pause.pauseStart!,
        end: pause.pauseEnd!,
      }));

    const totalPauseSeconds = completedPauses.reduce((total, pause) => {
      return total + differenceInSeconds(pause.end, pause.start);
    }, 0);

    const totalElapsedSeconds = differenceInSeconds(
      report.onPause
        ? pauseHistory.find((pause) => !pause.pauseEnd)?.pauseStart ||
            completedPauses[completedPauses.length - 1]?.end ||
            report.createdAt
        : now,
      report.createdAt,
    );

    const totalSeconds = totalElapsedSeconds - totalPauseSeconds;

    const lastValidTime = report.onPause
      ? completedPauses[completedPauses.length - 1]?.end || report.createdAt
      : now;

    return {
      totalSeconds,
      lastValidTime,
      isReportOnPause: report.onPause,
    };
  }
}
