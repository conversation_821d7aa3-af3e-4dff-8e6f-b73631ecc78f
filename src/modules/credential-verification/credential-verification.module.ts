import { Module, forwardRef } from '@nestjs/common';

import { CredentialVerificationController } from './credential-verification.controller';
import { CredentialVerificationService } from './credential-verification.service';
import { EmailModule } from '../email/email.module';
import { UsersModule } from '../users/users.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => NotificationsModule),
    EmailModule,
  ],
  controllers: [CredentialVerificationController],
  providers: [CredentialVerificationService],
  exports: [CredentialVerificationService],
})
export class CredentialVerificationModule {}
