import { VERSION_NEUTRAL, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { apiReference } from '@scalar/nestjs-api-reference';
import { NextFunction, Request, Response } from 'express';
import { patchNestJsSwagger } from 'nestjs-zod';

import './common/utils/crypto';
import { AppModule } from './app.module';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { DocsAuthMiddleware } from './common/middleware/docs-auth.middleware';
import { EnvType } from './config/env.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { cors: true });
  const config = app.get(ConfigService<EnvType>);
  const port = config.get('PORT');

  patchNestJsSwagger();

  app.useGlobalInterceptors(new ResponseInterceptor());

  const isDocsEnabled = config.get('API_DOCS_ENABLED');

  if (isDocsEnabled) {
    const docConfig = new DocumentBuilder()
      .setTitle('Group Working SST Backend')
      .setDescription('Backend for Group Working SST')
      .setVersion('1.0')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, docConfig);

    const docsAuthMiddleware = new DocsAuthMiddleware(config);

    SwaggerModule.setup('swagger', app, document, {
      jsonDocumentUrl: 'api/json',
    });

    app.use('/api/json', (req: Request, res: Response, next: NextFunction) =>
      docsAuthMiddleware.use(req, res, next),
    );

    app.use('/api', (req: Request, res: Response, next: NextFunction) =>
      docsAuthMiddleware.use(req, res, next),
    );

    app.use(
      '/api',
      apiReference({
        title: 'SSTW Backend',
        description: 'Backend for Group Working SSTW',
        version: '1.0',
        theme: 'kepler',
        layout: 'modern',
        defaultHttpClient: {
          targetKey: 'javascript',
          clientKey: 'fetch',
        },
        metaData: {
          title: 'SSTW Backend Docs',
        },
        spec: {
          url: '/api/json',
        },
      }),
    );
  }

  app.enableCors({
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });
  app.enableVersioning({
    type: VersioningType.HEADER,
    header: 'x-api-version',
    defaultVersion: VERSION_NEUTRAL,
  });

  app.useWebSocketAdapter(new IoAdapter(app));
  await app.listen(port);
}
bootstrap();
